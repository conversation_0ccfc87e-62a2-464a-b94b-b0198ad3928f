# 关卡卡片样式配置说明

## 概述
关卡滚动列表现在支持通过JSON配置每个关卡的背景颜色和图标，提供更丰富的视觉效果。

## JSON配置格式

### 完整的JSON结构
```json
{
  "config": {
    "bg_colors": [
      "68, 138, 202",
      "235, 194, 16", 
      "113, 87, 205"
    ],
    "level_icons": [
      "icon1",
      "icon2",
      "icon3"
    ]
  },
  "levels": {
    "1": {
      "characters": ["一", "二", "三", "四", "五"]
    },
    "2": {
      "characters": ["六", "七", "八", "九", "十"]
    }
    // ... 更多关卡
  }
}
```

### 配置字段说明

#### bg_colors（背景颜色）
- **格式**: 字符串数组，每个字符串表示RGB颜色值
- **颜色格式**: "R, G, B" 或 "R, G, B, A"（0-255范围）
- **示例**: 
  - `"68, 138, 202"` - 蓝色
  - `"235, 194, 16"` - 黄色
  - `"113, 87, 205"` - 紫色
- **循环使用**: 关卡数量超过颜色数量时会循环使用

#### level_icons（关卡图标）
- **格式**: 字符串数组，每个字符串是精灵的名称
- **精灵来源**: LC_LevelScrollList组件的sprite_list1列表
- **匹配规则**: 通过精灵的name属性匹配
- **循环使用**: 关卡数量超过图标数量时会循环使用

## 代码实现

### LC_LevelScrollList更新
```csharp
// 新增字段
public List<Sprite> sprite_list1; // 图标精灵列表

// 新增方法
private void ParseConfigData(System.Collections.Hashtable data)
private void ParseBackgroundColors()
private void ParseLevelIcons()
private Color GetBackgroundColorForLevel(int levelNumber)
private Sprite GetIconSpriteForLevel(int levelNumber)
```

### LC_LevelCard更新
```csharp
// 新增重载方法
public void InitializeCard(int level, Dictionary<string, object> data, Color backgroundColor, Sprite iconSprite)

// 新增辅助方法
private List<object> GetCharactersList()
```

## 设置步骤

### 步骤1：准备图标精灵
1. 将图标图片导入到Unity项目中
2. 设置图片的Texture Type为Sprite (2D and UI)
3. 记录每个精灵的名称

### 步骤2：配置sprite_list1
1. 选择场景中的LevelScrollView GameObject
2. 在LC_LevelScrollList组件中找到sprite_list1字段
3. 设置Size为图标数量
4. 将对应的精灵拖拽到列表中

### 步骤3：更新JSON配置
1. 编辑ChineseCharLevels-Phase1.json文件
2. 添加config部分，包含bg_colors和level_icons
3. 确保level_icons中的名称与精灵名称完全匹配

### 步骤4：测试效果
1. 运行场景
2. 检查每个关卡卡片是否显示了正确的背景颜色和图标
3. 验证颜色和图标的循环使用

## 使用示例

### 示例1：基础配置
```json
{
  "config": {
    "bg_colors": [
      "255, 99, 71",   // 番茄红
      "60, 179, 113",  // 海绿色
      "106, 90, 205"   // 石板蓝
    ],
    "level_icons": [
      "star",
      "heart", 
      "diamond"
    ]
  }
}
```

### 示例2：更多颜色选择
```json
{
  "config": {
    "bg_colors": [
      "255, 182, 193", // 浅粉色
      "173, 216, 230", // 浅蓝色
      "144, 238, 144", // 浅绿色
      "255, 218, 185", // 桃色
      "221, 160, 221"  // 梅红色
    ],
    "level_icons": [
      "book", "pencil", "apple", "star", "heart"
    ]
  }
}
```

## 颜色参考

### 推荐的儿童友好颜色
```
浅蓝色: "173, 216, 230"
浅绿色: "144, 238, 144"
浅粉色: "255, 182, 193"
浅黄色: "255, 255, 224"
浅紫色: "221, 160, 221"
浅橙色: "255, 218, 185"
```

### 鲜艳的主题色
```
红色: "255, 99, 71"
橙色: "255, 165, 0"
黄色: "255, 215, 0"
绿色: "50, 205, 50"
蓝色: "30, 144, 255"
紫色: "138, 43, 226"
```

## 故障排除

### 问题1：图标不显示
**可能原因**：
- 精灵名称与JSON中的名称不匹配
- sprite_list1列表为空或未正确设置
- 精灵导入设置不正确

**解决方案**：
1. 检查精灵名称是否完全匹配（区分大小写）
2. 确认sprite_list1中包含所需的精灵
3. 检查精灵的Texture Type设置

### 问题2：背景颜色不正确
**可能原因**：
- 颜色值格式错误
- RGB值超出0-255范围
- JSON格式错误

**解决方案**：
1. 检查颜色字符串格式："R, G, B"
2. 确认RGB值在0-255范围内
3. 验证JSON语法正确性

### 问题3：配置不生效
**可能原因**：
- JSON文件路径错误
- config字段缺失或格式错误
- 缓存问题

**解决方案**：
1. 确认JSON文件在正确的Resources路径下
2. 检查config字段的结构
3. 重新启动Unity编辑器清除缓存

## 扩展功能

### 自定义卡片样式
可以进一步扩展配置，支持：
- 文字颜色配置
- 边框样式配置
- 动画效果配置
- 特殊效果配置

### 主题系统
可以创建多套主题配置：
```json
{
  "themes": {
    "spring": {
      "bg_colors": ["浅绿色系"],
      "level_icons": ["花朵图标"]
    },
    "ocean": {
      "bg_colors": ["蓝色系"],
      "level_icons": ["海洋图标"]
    }
  }
}
```

## 性能考虑
- 图标精灵建议使用合适的分辨率（如64x64或128x128）
- 避免使用过大的图片文件
- 考虑使用图集（Atlas）来优化渲染性能
- 颜色解析在初始化时进行，不会影响运行时性能
