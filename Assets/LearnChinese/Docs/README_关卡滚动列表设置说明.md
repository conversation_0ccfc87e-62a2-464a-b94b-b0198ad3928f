# 关卡滚动列表设置说明

## 概述
在LearnChinese_Home2场景中创建了一个横向滚动的关卡列表，用于显示ChineseCharLevels-Phase1.json中的所有关卡。

## 已创建的组件

### 1. 脚本文件
- `LC_LevelScrollList.cs` - 关卡滚动列表管理器
- `LC_LevelCard.cs` - 关卡卡片组件

### 2. UI结构
在场景中已创建：
```
LevelScrollView (ScrollRect + LC_LevelScrollList)
├── Viewport
│   └── Content (HorizontalLayoutGroup + ContentSizeFitter)
└── Scrollbar Horizontal

LevelCard (Panel + Button + LC_LevelCard)
├── Title (TextMeshPro)
├── Characters (TextMeshPro)
└── Progress (TextMeshPro)
```

## 完成设置步骤

### 步骤1：创建关卡卡片预制体
1. 在场景中选择 `LevelCard` GameObject
2. 将其拖拽到 `Assets/LearnChinese/Prefabs/` 文件夹中
3. 重命名为 `LevelCard.prefab`
4. 删除场景中的 `LevelCard` GameObject

### 步骤2：设置LC_LevelScrollList组件
1. 选择场景中的 `LevelScrollView` GameObject
2. 在Inspector中找到 `LC_LevelScrollList` 组件
3. 设置以下属性：
   - **Scroll Rect**: 拖拽 `LevelScrollView` 自身
   - **Content Parent**: 拖拽 `LevelScrollView/Viewport/Content`
   - **Level Card Prefab**: 拖拽刚创建的 `LevelCard.prefab`
   - **Card Spacing**: 20
   - **Card Width**: 300
   - **Scroll Speed**: 1000

### 步骤3：调整卡片布局（可选）
1. 打开 `LevelCard.prefab` 预制体
2. 调整子元素位置：
   - 选择 `Title`，设置位置到顶部居中
   - 选择 `Characters`，设置位置到中间居中
   - 选择 `Progress`，设置位置到底部居中
3. 设置合适的字体大小和颜色
4. 保存预制体

### 步骤3：调整UI布局
1. 选择 `LevelScrollView`，设置RectTransform：
   - **Anchors**: 设置为适合的位置（如屏幕中央）
   - **Size**: 设置合适的宽高（如 Width: 800, Height: 200）

2. 选择 `LevelCard` 预制体，调整布局：
   - 设置卡片大小（建议 Width: 280, Height: 180）
   - 调整子元素位置：
     - `Title`: 顶部居中
     - `Characters`: 中间居中
     - `Progress`: 底部居中

### 步骤4：测试功能
1. 运行场景
2. 应该看到横向滚动的关卡列表
3. 每个卡片显示关卡标题、汉字预览和进度信息
4. 点击卡片应该有动画效果和控制台日志

## 功能特性

### 数据加载
- 自动从 `ChineseCharLevels-Phase1.json` 加载关卡数据
- 支持10个关卡，每个关卡5个汉字

### 横向滚动
- 使用Unity ScrollRect实现流畅滚动
- 支持鼠标拖拽和滚轮操作
- 自动计算Content大小

### 卡片交互
- 点击动画效果
- 音效支持（需要设置buttonClickSound）
- 可扩展的关卡加载逻辑

### 视觉效果
- 卡片缩放动画
- 颜色高亮效果
- 响应式布局

## 扩展功能

### 添加关卡进度
可以在 `LC_LevelCard.cs` 中添加进度显示：
```csharp
// 显示关卡完成状态
public void SetProgress(float progress)
{
    // 更新进度条或星级显示
}
```

### 添加关卡解锁
可以在 `LC_LevelScrollList.cs` 中添加解锁逻辑：
```csharp
// 检查关卡是否解锁
private bool IsLevelUnlocked(int levelNumber)
{
    // 返回关卡解锁状态
    return true;
}
```

### 自定义卡片样式
可以为不同关卡设置不同的背景图片或颜色主题。

## 注意事项
1. 确保JSON文件路径正确：`Resources/ChineseChars/ChineseCharLevels-Phase1.json`
2. 预制体引用必须正确设置
3. 如需添加音效，请在Inspector中设置buttonClickSound
4. 可以通过修改cardSpacing和cardWidth调整卡片布局

## 故障排除
- 如果卡片不显示：检查预制体引用和JSON文件路径
- 如果滚动不工作：检查ScrollRect的horizontal设置
- 如果布局错乱：检查HorizontalLayoutGroup和ContentSizeFitter设置
