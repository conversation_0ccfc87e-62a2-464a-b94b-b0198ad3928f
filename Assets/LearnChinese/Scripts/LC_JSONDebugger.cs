using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// JSON加载调试器
/// 用于测试和调试JSON文件加载问题
/// </summary>
public class LC_JSONDebugger : MonoBehaviour
{
    [Header("调试设置")]
    [SerializeField] private string jsonFileName = "ChineseCharLevels-Phase1";
    [SerializeField] private bool testOnStart = true;
    
    void Start()
    {
        if (testOnStart)
        {
            TestJSONLoading();
        }
    }
    
    /// <summary>
    /// 测试JSON加载
    /// </summary>
    [ContextMenu("Test JSON Loading")]
    public void TestJSONLoading()
    {
        Debug.Log("=== JSON加载调试开始 ===");
        
        // 测试1：直接加载TextAsset
        Debug.Log($"测试1：加载TextAsset - ChineseChars/{jsonFileName}");
        TextAsset jsonFile = Resources.Load<TextAsset>($"ChineseChars/{jsonFileName}");
        
        if (jsonFile == null)
        {
            Debug.LogError($"❌ TextAsset加载失败: ChineseChars/{jsonFileName}");
            
            // 尝试其他可能的路径
            Debug.Log("尝试其他路径...");
            jsonFile = Resources.Load<TextAsset>(jsonFileName);
            if (jsonFile != null)
            {
                Debug.Log($"✅ 在根目录找到文件: {jsonFileName}");
            }
            else
            {
                Debug.LogError($"❌ 在根目录也未找到文件: {jsonFileName}");
                return;
            }
        }
        else
        {
            Debug.Log($"✅ TextAsset加载成功: {jsonFile.name}");
        }
        
        // 测试2：检查文件内容
        Debug.Log("测试2：检查文件内容");
        string jsonString = jsonFile.text;
        
        if (string.IsNullOrEmpty(jsonString))
        {
            Debug.LogError("❌ JSON文件内容为空");
            return;
        }
        else
        {
            Debug.Log($"✅ JSON文件内容长度: {jsonString.Length} 字符");
            Debug.Log($"JSON前100个字符: {jsonString.Substring(0, Mathf.Min(100, jsonString.Length))}...");
        }
        
        // 测试3：JSON解析
        Debug.Log("测试3：JSON解析");
        try
        {
            var data = MiniJsonExtensions.hashtableFromJson(jsonString);
            if (data == null)
            {
                Debug.LogError("❌ JSON解析返回null");
                return;
            }
            else
            {
                Debug.Log($"✅ JSON解析成功，包含 {data.Count} 个顶级字段");
                foreach (var key in data.Keys)
                {
                    Debug.Log($"  - 字段: {key}");
                }
            }
            
            // 测试4：检查levels字段
            Debug.Log("测试4：检查levels字段");
            if (!data.ContainsKey("levels"))
            {
                Debug.LogError("❌ JSON中缺少'levels'字段");
                return;
            }
            
            var levelsObject = data["levels"];
            Debug.Log($"levels字段实际类型: {levelsObject?.GetType()}");

            Dictionary<string, object> levelsData = null;

            // 处理不同的数据类型
            if (levelsObject is System.Collections.Hashtable levelsHashtable)
            {
                Debug.Log("✅ levels是Hashtable类型，正在转换...");
                levelsData = new Dictionary<string, object>();
                foreach (System.Collections.DictionaryEntry entry in levelsHashtable)
                {
                    levelsData[entry.Key.ToString()] = entry.Value;
                }
            }
            else if (levelsObject is Dictionary<string, object> levelsDictionary)
            {
                Debug.Log("✅ levels是Dictionary类型");
                levelsData = levelsDictionary;
            }
            else
            {
                Debug.LogError($"❌ 'levels'字段类型不支持: {levelsObject?.GetType()}");
                return;
            }

            Debug.Log($"✅ levels字段解析成功，包含 {levelsData.Count} 个关卡");

            // 测试5：检查关卡内容
            Debug.Log("测试5：检查关卡内容");
            foreach (var levelKey in levelsData.Keys)
            {
                var levelInfo = levelsData[levelKey];
                Debug.Log($"  关卡 {levelKey} 类型: {levelInfo?.GetType()}");

                if (levelInfo is System.Collections.Hashtable levelHashtable)
                {
                    if (levelHashtable.ContainsKey("characters"))
                    {
                        var charactersObject = levelHashtable["characters"];
                        Debug.Log($"    characters类型: {charactersObject?.GetType()}");

                        if (charactersObject is System.Collections.ArrayList charactersArray)
                        {
                            Debug.Log($"    关卡 {levelKey}: {charactersArray.Count} 个汉字");
                            for (int i = 0; i < Math.Min(3, charactersArray.Count); i++)
                            {
                                Debug.Log($"      汉字 {i + 1}: {charactersArray[i]}");
                            }
                        }
                    }
                }
                else if (levelInfo is Dictionary<string, object> levelDict)
                {
                    if (levelDict.ContainsKey("characters"))
                    {
                        var characters = levelDict["characters"] as List<object>;
                        if (characters != null)
                        {
                            Debug.Log($"    关卡 {levelKey}: {characters.Count} 个汉字");
                        }
                    }
                }
            }
            
            Debug.Log("✅ 所有测试通过！JSON文件加载和解析正常");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ JSON解析异常: {e.Message}");
            Debug.LogError($"堆栈跟踪: {e.StackTrace}");
        }
        
        Debug.Log("=== JSON加载调试结束 ===");
    }
    
    /// <summary>
    /// 列出Resources文件夹中的所有JSON文件
    /// </summary>
    [ContextMenu("List All JSON Files")]
    public void ListAllJSONFiles()
    {
        Debug.Log("=== Resources文件夹中的JSON文件 ===");
        
        // 尝试加载一些常见的JSON文件
        string[] possibleFiles = {
            "ChineseCharLevels-Phase1",
            "ChineseCharLevels-Phase2", 
            "ChineseCharList-Phase1",
            "ChineseCharList-Phase2",
            "ChineseCharList_Example"
        };
        
        foreach (string fileName in possibleFiles)
        {
            TextAsset file = Resources.Load<TextAsset>($"ChineseChars/{fileName}");
            if (file != null)
            {
                Debug.Log($"✅ 找到文件: ChineseChars/{fileName}");
            }
            else
            {
                Debug.Log($"❌ 未找到文件: ChineseChars/{fileName}");
            }
        }
    }
}
