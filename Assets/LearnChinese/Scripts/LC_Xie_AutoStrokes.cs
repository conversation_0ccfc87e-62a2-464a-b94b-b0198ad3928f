using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro; // 添加TextMeshPro命名空间
using UnityEngine.EventSystems; // 添加事件系统命名空间
using DG.Tweening;
using System.Linq;
using UnityEngine.UI; // 添加UI命名空间
using MiniJSON_New; // 添加JSON解析命名空间
using UnityEngine.Video; // 添加视频播放命名空间
using System; // 添加系统命名空间

public class  LC_Xie_AutoStrokes : MonoBehaviour
{
    [Header("学习设置")]
    [SerializeField] private string currentCharacter = "一"; // 当前要显示的汉字

    [SerializeField] private AudioSource audioSource;
    
    [SerializeField] private AudioClip tutorial_sound;
    
    [SerializeField] private GameObject replayButton; // 重新播放按钮
    [SerializeField] private GameObject pausePlayButton; // 暂停/播放按钮
    
    [Header("视频设置")]
    [SerializeField] private VideoPlayer videoPlayer; // 视频播放器组件
    [SerializeField] private RawImage videoDisplay; // 显示视频的UI组件
    [SerializeField] private float videoLoadTimeout = 5.0f; // 视频加载超时时间（秒）
    [SerializeField] private bool enableVideoClickReplay = true; // 是否启用点击视频重播功能
    [SerializeField] private bool enableClickFeedback = false; // 是否启用点击反馈效果
    
    [Header("UI设置")]
    [SerializeField] private GameObject loadingIndicator; // 加载指示器
    [SerializeField] private Image videoBackground; // 视频背景
    [SerializeField] private Color loadingBackgroundColor = new Color(0.8f, 0.8f, 0.8f, 1f); // 加载时的背景颜色

    [Header("按笔画播放设置")]
    [SerializeField] private bool enableStrokeByStrokeMode = false; // 是否启用按笔画播放模式
    [SerializeField] private float strokeDuration = 1000f; // 每个笔画的绘制时间(ms)
    [SerializeField] private float delayBetweenStrokes = 1500f; // 笔画间的间隔时间(ms)

    [Header("汉字笔画渲染设置")]
    [SerializeField] private bool enableSVGRendering = true; // 是否启用SVG笔画渲染
    [SerializeField] private Transform strokeContainer; // 笔画渲染容器
    [SerializeField] private Canvas strokeCanvas; // 笔画渲染画布
    [SerializeField] private Color strokeColor = Color.black; // 笔画颜色
    [SerializeField] private float strokeWidth = 30f; // 笔画宽度
    [SerializeField] private bool showStrokeOutline = true; // 是否显示笔画外形
    [SerializeField] private bool showGrid = true; // 是否显示网格
    [SerializeField] private Color gridColor = new Color(0.7f, 0.7f, 0.7f, 0.6f); // 网格颜色

    [Header("轮廓渲染设置")]
    [SerializeField] private bool useOutlineRendering = true; // 是否使用轮廓渲染（圆点描边）
    [SerializeField] private float outlineDotSize = 3f; // 轮廓圆点大小
    [SerializeField] private float outlineDotSpacing = 8f; // 轮廓圆点间距
    [SerializeField] private Color outlineDotColor = Color.black; // 轮廓圆点颜色
    
    // 配置文件路径
    private string CONFIG_PATH = "ChineseChars/ChineseCharList";
    private string videoFolderPath = "ChineseChars/Videos"; // 默认视频文件夹路径
    private string videoName = ""; // 视频文件名
    private bool isVideoReady = false; // 视频是否准备好
    private bool isVideoPaused = false; // 视频是否暂停

    // 按笔画播放相关变量
    private SimpleHanziData strokeData; // 笔画数据
    private int currentStrokeIndex = 0; // 当前笔画索引
    private float[] strokeTimings; // 每个笔画的时间点
    private bool isStrokeByStrokeActive = false; // 是否正在按笔画播放
    private Coroutine videoProgressMonitor; // 视频进度监控协程

    // 汉字笔画渲染相关变量
    private HanziWriter hanziWriter; // 汉字书写器
    private List<StrokeRenderer> strokeRenderers = new List<StrokeRenderer>(); // 笔画渲染器列表
    private bool isStrokeRenderingInitialized = false; // 笔画渲染是否已初始化
    private HanziData hanziRenderData; // 用于渲染的汉字数据
    
    // Start is called before the first frame update
    void Start()
    {
        CONFIG_PATH = "ChineseChars/ChineseCharList-Phase1";
        InitializeGame();
        
        // 设置重新播放按钮的点击事件
        if (replayButton != null)
        {
            Button replayBtn = replayButton.GetComponent<Button>();
            LC_ReplaySoundBtn replaySoundBtn = replayButton.GetComponent<LC_ReplaySoundBtn>();

            if (replayBtn != null)
            {
                replayBtn.onClick.RemoveAllListeners();
                replayBtn.onClick.AddListener(() => {
                    // 重播按钮只播放汉字语音和视频动画，不播放引导语
                    PlayCharacterSoundAndVideo();
                    // 确保按钮动画效果被触发
                    if (replaySoundBtn != null)
                    {
                        replaySoundBtn.OnButtonClick();
                    }
                });
            }
        }

        // 设置暂停/播放按钮的点击事件
        if (pausePlayButton != null)
        {
            Button pausePlayBtn = pausePlayButton.GetComponent<Button>();
            if (pausePlayBtn != null)
            {
                pausePlayBtn.onClick.RemoveAllListeners();
                pausePlayBtn.onClick.AddListener(() => {
                    ToggleVideoPause();
                });
            }
        }
    }

    // 清理资源
    private void OnDestroy()
    {
        // 移除视频播放完成事件监听
        if (videoPlayer != null)
        {
            videoPlayer.loopPointReached -= OnVideoCompleted;
        }

        // 清理笔画渲染器
        if (strokeRenderers != null)
        {
            ClearStrokeRenderers();
        }
    }

    // 公共方法，可以从外部调用重新播放引导语音和当前汉字语音
    public void ReplayTutorialAndCharacter()
    {
        PlayTutorialAndCharacterSound();
    }
    
    // 新增方法：只播放汉字语音和视频动画（用于重播按钮）
    public void PlayCharacterSoundAndVideo()
    {
        // 播放汉字语音
        PlayCharacterSound(currentCharacter);
        
        // 播放视频动画
        PlayWritingVideo();
    }
    
    // 启用或禁用视频点击重播功能
    public void SetVideoClickReplayEnabled(bool enabled)
    {
        enableVideoClickReplay = enabled;
        
        if (videoDisplay != null)
        {
            EventTrigger eventTrigger = videoDisplay.GetComponent<EventTrigger>();
            if (enabled)
            {
                // 启用时设置点击事件
                if (eventTrigger == null)
                {
                    SetupVideoClickEvent();
                }
            }
            else
            {
                // 禁用时移除点击事件
                if (eventTrigger != null)
                {
                    eventTrigger.triggers.Clear();
                }
            }
        }
    }
    
    // 获取当前视频点击重播功能状态
    public bool IsVideoClickReplayEnabled()
    {
        return enableVideoClickReplay;
    }

    // 切换视频暂停/播放状态
    public void ToggleVideoPause()
    {
        if (videoPlayer != null && isVideoReady)
        {
            if (enableStrokeByStrokeMode && strokeData != null)
            {
                // 按笔画播放模式
                if (currentStrokeIndex == -1)
                {
                    // 所有笔画播放完成，重新开始
                    Debug.Log("ToggleVideoPause: 检测到currentStrokeIndex=-1，调用RestartStrokeByStrokePlayback");
                    RestartStrokeByStrokePlayback();
                }
                else if (isStrokeByStrokeActive)
                {
                    // 正在按笔画播放
                    if (isVideoPaused)
                    {
                        // 当前已暂停，播放下一个笔画
                        PlayNextStroke();
                    }
                    else if (videoPlayer.isPlaying)
                    {
                        // 当前正在播放，暂停视频
                        PauseVideo();
                    }
                }
                else
                {
                    // 按笔画模式但未开始播放，开始播放
                    PlayWritingVideo();
                }
            }
            else
            {
                // 正常播放模式
                if (videoPlayer.isPlaying)
                {
                    // 当前正在播放，暂停视频
                    PauseVideo();
                }
                else if (isVideoPaused)
                {
                    // 当前已暂停，继续播放
                    ResumeVideo();
                }
                else
                {
                    // 视频未播放且未暂停，开始播放
                    PlayWritingVideo();
                }
            }
        }
        else
        {
            Debug.LogWarning("视频播放器未准备好或视频资源未加载");
        }
    }

    // 暂停视频
    public void PauseVideo()
    {
        if (videoPlayer != null && videoPlayer.isPlaying)
        {
            videoPlayer.Pause();
            isVideoPaused = true;
            Debug.Log("视频已暂停");

            // 更新按钮显示（如果需要的话）
            UpdatePausePlayButtonDisplay();
        }
    }

    // 继续播放视频
    public void ResumeVideo()
    {
        if (videoPlayer != null && isVideoPaused)
        {
            videoPlayer.Play();
            isVideoPaused = false;
            Debug.Log("视频继续播放");

            // 更新按钮显示（如果需要的话）
            UpdatePausePlayButtonDisplay();
        }
    }

    // 获取视频是否正在播放
    public bool IsVideoPlaying()
    {
        return videoPlayer != null && videoPlayer.isPlaying;
    }

    // 获取视频是否暂停
    public bool IsVideoPaused()
    {
        return isVideoPaused;
    }

    // 启用或禁用按笔画播放模式
    public void SetStrokeByStrokeMode(bool enabled)
    {
        enableStrokeByStrokeMode = enabled;

        if (enabled)
        {
            // 加载笔画数据
            LoadStrokeData();
        }
        else
        {
            // 停止按笔画播放模式
            StopStrokeByStrokeMode();
        }

        Debug.Log($"按笔画播放模式: {(enabled ? "启用" : "禁用")}");
    }

    // 获取按笔画播放模式状态
    public bool IsStrokeByStrokeModeEnabled()
    {
        return enableStrokeByStrokeMode;
    }

    // 获取当前笔画信息
    public string GetCurrentStrokeInfo()
    {
        if (isStrokeByStrokeActive && strokeData != null)
        {
            return $"笔画 {currentStrokeIndex + 1}/{strokeData.strokes.Length}";
        }
        return "";
    }

    // 测试方法：手动完成笔画播放（用于调试）
    public void TestCompleteStrokePlayback()
    {
        Debug.Log("手动测试完成笔画播放");
        CompleteStrokeByStrokePlayback();
    }

    // 获取当前状态信息（用于调试）
    public string GetDebugInfo()
    {
        return $"enableStrokeByStrokeMode: {enableStrokeByStrokeMode}, " +
               $"currentStrokeIndex: {currentStrokeIndex}, " +
               $"isStrokeByStrokeActive: {isStrokeByStrokeActive}, " +
               $"strokeData != null: {strokeData != null}, " +
               $"isVideoPaused: {isVideoPaused}, " +
               $"videoPlayer.isPlaying: {(videoPlayer != null ? videoPlayer.isPlaying.ToString() : "null")}";
    }

    // 手动重新开始（用于调试）
    public void DebugRestartStrokePlayback()
    {
        Debug.Log("=== 手动调试重新开始 ===");
        Debug.Log($"调用前状态: {GetDebugInfo()}");
        RestartStrokeByStrokePlayback();
        Debug.Log($"调用后状态: {GetDebugInfo()}");
    }

    // 手动设置重新开始状态（用于调试）
    public void DebugSetRestartState()
    {
        Debug.Log("=== 手动设置重新开始状态 ===");
        currentStrokeIndex = -1;
        isStrokeByStrokeActive = false;
        isVideoPaused = false;

        // 手动启用按钮
        if (pausePlayButton != null)
        {
            Button buttonComponent = pausePlayButton.GetComponent<Button>();
            TextMeshProUGUI buttonText = pausePlayButton.GetComponentInChildren<TextMeshProUGUI>();

            if (buttonText != null)
            {
                buttonText.text = "重新开始";
            }

            if (buttonComponent != null)
            {
                buttonComponent.interactable = true;
                Debug.Log("按钮已手动启用");
            }
        }

        Debug.Log($"设置后状态: {GetDebugInfo()}");
    }

    // 播放下一个笔画
    public void PlayNextStroke()
    {
        if (isStrokeByStrokeActive && strokeData != null)
        {
            if (currentStrokeIndex < strokeData.strokes.Length - 1)
            {
                currentStrokeIndex++;
                ContinueToNextStroke();
            }
            else
            {
                // 所有笔画播放完成
                CompleteStrokeByStrokePlayback();
            }
        }
    }

    // 更新暂停/播放按钮的显示状态
    private void UpdatePausePlayButtonDisplay()
    {
        if (pausePlayButton != null)
        {
            // 获取按钮组件
            Button buttonComponent = pausePlayButton.GetComponent<Button>();
            TextMeshProUGUI buttonText = pausePlayButton.GetComponentInChildren<TextMeshProUGUI>();

            Debug.Log($"UpdatePausePlayButtonDisplay: buttonComponent={buttonComponent != null}, buttonText={buttonText != null}");

            if (buttonText != null)
            {
                if (enableStrokeByStrokeMode && strokeData != null)
                {
                    // 按笔画播放模式
                    Debug.Log($"UpdatePausePlayButtonDisplay: currentStrokeIndex={currentStrokeIndex}, isStrokeByStrokeActive={isStrokeByStrokeActive}, isVideoPaused={isVideoPaused}");

                    if (currentStrokeIndex == -1)
                    {
                        // 所有笔画播放完成状态
                        buttonText.text = "重新开始";
                        if (buttonComponent != null) buttonComponent.interactable = true;
                        Debug.Log("按钮文本设置为: 重新开始，按钮启用");
                    }
                    else if (isStrokeByStrokeActive)
                    {
                        // 正在按笔画播放
                        if (isVideoPaused)
                        {
                            // 笔画播放完成，等待用户点击继续
                            buttonText.text = $"继续 ({currentStrokeIndex + 1}/{strokeData.strokes.Length})";
                            if (buttonComponent != null) buttonComponent.interactable = true;
                            Debug.Log($"按钮文本设置为: 继续 ({currentStrokeIndex + 1}/{strokeData.strokes.Length})，按钮启用");
                        }
                        else
                        {
                            // 笔画正在播放中，禁用按钮
                            buttonText.text = $"播放中 ({currentStrokeIndex + 1}/{strokeData.strokes.Length})";
                            if (buttonComponent != null) buttonComponent.interactable = false;
                            Debug.Log($"按钮文本设置为: 播放中 ({currentStrokeIndex + 1}/{strokeData.strokes.Length})，按钮禁用");
                        }
                    }
                    else
                    {
                        // 按笔画模式但未开始播放
                        buttonText.text = $"开始 (1/{strokeData.strokes.Length})";
                        if (buttonComponent != null) buttonComponent.interactable = true;
                    }
                }
                else
                {
                    // 正常播放模式
                    if (videoPlayer != null && videoPlayer.isPlaying)
                    {
                        buttonText.text = "暂停";
                    }
                    else if (isVideoPaused)
                    {
                        buttonText.text = "播放";
                    }
                    else
                    {
                        buttonText.text = "播放";
                    }
                }
            }

            // 如果按钮有Image组件，可以更改图标
            Image buttonImage = pausePlayButton.GetComponent<Image>();
            if (buttonImage != null)
            {
                // 这里可以根据状态切换不同的Sprite
                // 需要在Inspector中设置对应的暂停和播放图标
            }
        }
    }
    
    private void InitializeAudio()
    {
        if (audioSource == null)
        {
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
        }

        // 设置音频源属性
        audioSource.playOnAwake = false;
        audioSource.spatialBlend = 0.0f;
        audioSource.volume = 1.0f;
    }

    // 初始化汉字笔画渲染
    private void InitializeStrokeRendering()
    {
        try
        {
            // 确保有笔画容器
            if (strokeContainer == null)
            {
                // 尝试在场景中查找或创建笔画容器
                GameObject containerObj = GameObject.Find("StrokeContainer");
                if (containerObj == null)
                {
                    containerObj = new GameObject("StrokeContainer");
                    containerObj.transform.SetParent(transform);
                }
                strokeContainer = containerObj.transform;
            }

            // 确保有画布
            if (strokeCanvas == null)
            {
                strokeCanvas = GetComponentInParent<Canvas>();
                if (strokeCanvas == null)
                {
                    Debug.LogWarning("未找到Canvas组件，笔画渲染可能无法正常显示");
                }
            }

            // 加载汉字笔画数据
            LoadHanziRenderData();

            // 创建笔画渲染器
            if (hanziRenderData != null)
            {
                CreateStrokeRenderers();
                isStrokeRenderingInitialized = true;
                Debug.Log($"汉字笔画渲染初始化完成: {currentCharacter}");
            }
            else
            {
                Debug.LogWarning($"无法加载汉字 '{currentCharacter}' 的笔画数据，跳过SVG渲染");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"初始化笔画渲染时出错: {e.Message}");
            enableSVGRendering = false;
        }
    }

    // 加载汉字渲染数据
    private void LoadHanziRenderData()
    {
        try
        {
            // 构建笔画数据文件路径
            string strokeDataPath = $"ChineseChars/Strokes/{currentCharacter}";
            TextAsset strokeFile = Resources.Load<TextAsset>(strokeDataPath);

            if (strokeFile != null)
            {
                // 解析笔画数据
                SimpleHanziData simpleData = JsonUtility.FromJson<SimpleHanziData>(strokeFile.text);

                if (simpleData != null && simpleData.strokes != null && simpleData.strokes.Length > 0)
                {
                    // 转换为HanziData格式
                    hanziRenderData = HanziDataLoader.ConvertSimpleDataToHanziData(strokeFile.text, currentCharacter);

                    if (hanziRenderData != null)
                    {
                        Debug.Log($"成功加载汉字渲染数据: {currentCharacter}, 共 {hanziRenderData.strokes.Length} 个笔画");
                    }
                    else
                    {
                        Debug.LogError($"转换汉字数据失败: {currentCharacter}");
                    }
                }
                else
                {
                    Debug.LogError($"笔画数据格式错误: {currentCharacter}");
                }
            }
            else
            {
                Debug.LogWarning($"未找到笔画数据文件: {strokeDataPath}");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"加载汉字渲染数据时出错: {e.Message}");
            hanziRenderData = null;
        }
    }

    // 创建笔画渲染器
    private void CreateStrokeRenderers()
    {
        if (hanziRenderData == null || hanziRenderData.strokes == null || strokeContainer == null)
        {
            Debug.LogWarning("无法创建笔画渲染器：数据或容器缺失");
            return;
        }

        // 清除现有的笔画渲染器
        ClearStrokeRenderers();

        try
        {
            // 为每个笔画创建渲染器
            for (int i = 0; i < hanziRenderData.strokes.Length; i++)
            {
                // 创建UI GameObject，使用RectTransform
                GameObject strokeObj = new GameObject($"Stroke_{i + 1}");
                strokeObj.transform.SetParent(strokeContainer);

                // 添加RectTransform组件
                RectTransform rectTransform = strokeObj.AddComponent<RectTransform>();
                // 不要填满整个容器，让每个笔画保持自己的位置和大小
                rectTransform.anchorMin = new Vector2(0.5f, 0.5f); // 锚点在中心
                rectTransform.anchorMax = new Vector2(0.5f, 0.5f); // 锚点在中心
                rectTransform.anchoredPosition = Vector2.zero; // 位置在容器中心
                rectTransform.sizeDelta = new Vector2(512f, 512f); // 设置固定大小

                // 添加StrokeRenderer组件
                StrokeRenderer sr = strokeObj.AddComponent<StrokeRenderer>();

                // 设置基本属性
                sr.strokeColor = strokeColor;
                sr.strokeWidth = strokeWidth;
                sr.animationDuration = strokeDuration / 1000f; // 转换为秒

                // 设置网格属性
                sr.showGrid = showGrid;
                sr.gridColor = gridColor;
                sr.gridSize = 200f;

                // 设置边框属性
                sr.showBorder = true;
                sr.borderColor = new Color(1f, 0.5f, 0f, 1f);
                sr.borderWidth = 2f;

                // 设置轮廓渲染属性
                sr.useOutlineRendering = useOutlineRendering;
                sr.dotSize = outlineDotSize;
                sr.dotSpacing = outlineDotSpacing;
                sr.dotColor = outlineDotColor;

                // 优先使用medians数据，因为它是笔画的中心线
                if (hanziRenderData.strokes[i].medians != null && hanziRenderData.strokes[i].medians.Length > 0)
                {
                    sr.SetStrokeDataWithMedians(hanziRenderData.strokes[i].medians);
                    Debug.Log($"笔画 {i + 1} 使用中位数数据，起始点: {hanziRenderData.strokes[i].medians[0]}");
                }
                else if (!string.IsNullOrEmpty(hanziRenderData.strokes[i].svgPath))
                {
                    sr.SetStrokeData(hanziRenderData.strokes[i].svgPath);
                    Debug.Log($"笔画 {i + 1} 使用SVG路径: {hanziRenderData.strokes[i].svgPath.Substring(0, Math.Min(50, hanziRenderData.strokes[i].svgPath.Length))}...");
                }
                else
                {
                    Debug.LogError($"笔画 {i + 1} 没有可用的数据");
                    continue;
                }

                strokeRenderers.Add(sr);
            }

            Debug.Log($"成功创建 {strokeRenderers.Count} 个笔画渲染器");

            // 显示汉字外形
            if (showStrokeOutline)
            {
                ShowCharacterOutline();
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"创建笔画渲染器时出错: {e.Message}");
        }
    }

    // 清除笔画渲染器
    private void ClearStrokeRenderers()
    {
        foreach (var renderer in strokeRenderers)
        {
            if (renderer != null && renderer.gameObject != null)
            {
                DestroyImmediate(renderer.gameObject);
            }
        }
        strokeRenderers.Clear();
    }

    // 显示汉字外形
    private void ShowCharacterOutline()
    {
        if (strokeRenderers == null || strokeRenderers.Count == 0)
        {
            Debug.LogWarning("没有可用的笔画渲染器来显示汉字外形");
            return;
        }

        try
        {
            // 显示所有笔画的外形（不播放动画）
            foreach (var renderer in strokeRenderers)
            {
                if (renderer != null)
                {
                    // 清除当前笔画
                    renderer.ClearStroke();
                    // 不播放动画，直接显示完整笔画
                    // 这里可以添加显示完整笔画外形的逻辑
                }
            }

            Debug.Log($"显示汉字 '{currentCharacter}' 的外形，共 {strokeRenderers.Count} 个笔画");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"显示汉字外形时出错: {e.Message}");
        }
    }

    // 播放笔画动画
    public void PlayStrokeAnimation()
    {
        if (!isStrokeRenderingInitialized || strokeRenderers == null || strokeRenderers.Count == 0)
        {
            Debug.LogWarning("笔画渲染未初始化或没有可用的渲染器");
            return;
        }

        StartCoroutine(PlayStrokeAnimationCoroutine());
    }

    private IEnumerator PlayStrokeAnimationCoroutine()
    {
        // 清除所有笔画
        foreach (var renderer in strokeRenderers)
        {
            if (renderer != null)
            {
                renderer.ClearStroke();
            }
        }

        // 按顺序播放每个笔画
        for (int i = 0; i < strokeRenderers.Count; i++)
        {
            if (strokeRenderers[i] != null)
            {
                Debug.Log($"播放笔画 {i + 1}/{strokeRenderers.Count}");
                strokeRenderers[i].AnimateStroke(strokeDuration / 1000f);

                // 等待当前笔画完成
                yield return new WaitForSeconds(strokeDuration / 1000f);

                // 笔画间隔
                if (i < strokeRenderers.Count - 1)
                {
                    yield return new WaitForSeconds(delayBetweenStrokes / 1000f);
                }
            }
        }

        Debug.Log("笔画动画播放完成");
    }

    // 公共方法：启用或禁用SVG渲染
    public void SetSVGRenderingEnabled(bool enabled)
    {
        enableSVGRendering = enabled;

        if (enabled && !isStrokeRenderingInitialized)
        {
            InitializeStrokeRendering();
        }
        else if (!enabled && isStrokeRenderingInitialized)
        {
            ClearStrokeRenderers();
            isStrokeRenderingInitialized = false;
        }

        Debug.Log($"SVG渲染: {(enabled ? "启用" : "禁用")}");
    }

    // 公共方法：获取SVG渲染状态
    public bool IsSVGRenderingEnabled()
    {
        return enableSVGRendering && isStrokeRenderingInitialized;
    }

    // 公共方法：设置笔画颜色
    public void SetStrokeColor(Color color)
    {
        strokeColor = color;

        if (strokeRenderers != null)
        {
            foreach (var renderer in strokeRenderers)
            {
                if (renderer != null)
                {
                    renderer.SetStrokeColor(color);
                }
            }
        }
    }

    // 公共方法：设置笔画宽度
    public void SetStrokeWidth(float width)
    {
        strokeWidth = width;

        if (strokeRenderers != null)
        {
            foreach (var renderer in strokeRenderers)
            {
                if (renderer != null)
                {
                    renderer.SetStrokeWidth(width);
                }
            }
        }
    }

    // 公共方法：显示或隐藏汉字外形
    public void SetShowStrokeOutline(bool show)
    {
        showStrokeOutline = show;

        if (show && isStrokeRenderingInitialized)
        {
            ShowCharacterOutline();
        }
        else if (!show && strokeRenderers != null)
        {
            foreach (var renderer in strokeRenderers)
            {
                if (renderer != null)
                {
                    renderer.ClearStroke();
                }
            }
        }
    }

    // 公共方法：重新加载当前汉字的笔画数据
    public void ReloadCharacterStrokeData()
    {
        if (enableSVGRendering)
        {
            LoadHanziRenderData();

            if (hanziRenderData != null)
            {
                CreateStrokeRenderers();

                if (showStrokeOutline)
                {
                    ShowCharacterOutline();
                }
            }
        }
    }

    // 从配置文件加载视频配置
    private bool LoadVideoConfig()
    {
        try
        {
            // 加载配置文件
            TextAsset configFile = Resources.Load<TextAsset>(CONFIG_PATH);
            if (configFile == null)
            {
                Debug.LogError($"无法加载配置文件: {CONFIG_PATH}");
                return false;
            }
            
            // 解析JSON
            Dictionary<string, object> jsonData = Json.Deserialize(configFile.text) as Dictionary<string, object>;
            if (jsonData == null)
            {
                Debug.LogError("配置文件JSON解析失败");
                return false;
            }
            
            // 获取配置信息
            if (jsonData.ContainsKey("config"))
            {
                Dictionary<string, object> config = jsonData["config"] as Dictionary<string, object>;
                if (config != null && config.ContainsKey("video_folder"))
                {
                    videoFolderPath = config["video_folder"] as string;
                    Debug.Log($"从配置文件加载视频文件夹路径: {videoFolderPath}");
                }
            }
            
            // 获取当前汉字的视频信息
            if (jsonData.ContainsKey("characters"))
            {
                Dictionary<string, object> characters = jsonData["characters"] as Dictionary<string, object>;
                if (characters != null && characters.ContainsKey(currentCharacter))
                {
                    Dictionary<string, object> charData = characters[currentCharacter] as Dictionary<string, object>;
                    if (charData != null && charData.ContainsKey("video"))
                    {
                        Dictionary<string, object> videoData = charData["video"] as Dictionary<string, object>;
                        if (videoData != null && videoData.ContainsKey("video_name"))
                        {
                            videoName = videoData["video_name"] as string;
                            Debug.Log($"从配置文件加载视频名称: {videoName}");
                            return true;
                        }
                    }
                    else
                    {
                        Debug.LogWarning($"汉字 '{currentCharacter}' 在配置文件中没有视频信息");
                    }
                }
                else
                {
                    Debug.LogError($"在配置文件中找不到汉字: {currentCharacter}");
                }
            }
            
            return false;
        }
        catch (Exception e)
        {
            Debug.LogError($"加载视频配置时出错: {e.Message}");
            return false;
        }
    }

    private void InitializeVideo()
    {
        // 初始化视频播放器
        if (videoPlayer == null)
        {
            videoPlayer = GetComponent<VideoPlayer>();
            if (videoPlayer == null)
            {
                videoPlayer = gameObject.AddComponent<VideoPlayer>();
            }
        }
        
        // 设置视频播放器属性
        videoPlayer.playOnAwake = false;
        videoPlayer.isLooping = false;
        videoPlayer.renderMode = VideoRenderMode.RenderTexture;

        // 添加视频播放完成事件监听
        videoPlayer.loopPointReached += OnVideoCompleted;
        
        // 如果有视频显示UI组件，设置渲染目标
        if (videoDisplay != null)
        {
            // 创建一个渲染纹理
            RenderTexture renderTexture = new RenderTexture(1280, 720, 24);
            videoPlayer.targetTexture = renderTexture;
            videoDisplay.texture = renderTexture;
            
            // 初始时隐藏视频显示
            SetVideoDisplayVisible(false);
            
            // 设置视频点击事件
            SetupVideoClickEvent();
        }
        
        // 显示加载指示器
        ShowLoadingIndicator(true);
        
        // 加载视频配置并尝试加载视频资源
        if (LoadVideoConfig())
        {
            StartCoroutine(LoadVideoFromConfig());
        }
        else
        {
            Debug.LogError("无法加载视频配置，视频将无法播放");
            // 隐藏加载指示器
            ShowLoadingIndicator(false);
        }
    }
    
    // 初始化加载指示器
    private void InitializeLoadingIndicator()
    {
        // 显示加载指示器
        ShowLoadingIndicator(true);
    }
    
    // 显示或隐藏加载指示器
    private void ShowLoadingIndicator(bool show)
    {
        if (loadingIndicator != null)
        {
            loadingIndicator.SetActive(show);
            
            // 获取加载指示器中的Image组件
            Image loadingImage = loadingIndicator.GetComponentInChildren<Image>();
            if (loadingImage != null)
            {
                // 确保Image在加载时可见
                Color imageColor = loadingImage.color;
                imageColor.a = show ? 1f : 0f;
                loadingImage.color = imageColor;
            }
        }
        else
        {
            // 如果没有找到LoadingIndicator，尝试在场景中查找
            Debug.LogWarning("LoadingIndicator未设置，尝试在场景中查找");
            GameObject foundIndicator = GameObject.Find("LoadingIndicator");
            if (foundIndicator != null)
            {
                loadingIndicator = foundIndicator;
                loadingIndicator.SetActive(show);
            }
        }
        
        // 设置背景颜色
        if (videoBackground != null)
        {
            // 如果显示加载指示器，使用白色背景而非灰色
            videoBackground.color = Color.white;
        }
    }
    
    // 设置视频显示是否可见
    private void SetVideoDisplayVisible(bool visible)
    {
        if (videoDisplay != null)
        {
            // 直接设置GameObject的激活状态，而不是只改变透明度
            videoDisplay.gameObject.SetActive(visible);
            
            // 同时设置透明度
            Color color = videoDisplay.color;
            color.a = visible ? 1f : 0f;
            videoDisplay.color = color;
            
            // 确保点击事件正常工作
            if (visible && enableVideoClickReplay)
            {
                // 确保有EventTrigger组件
                EventTrigger eventTrigger = videoDisplay.GetComponent<EventTrigger>();
                if (eventTrigger == null)
                {
                    SetupVideoClickEvent();
                }
            }
        }
    }
    
    // 设置视频点击事件
    private void SetupVideoClickEvent()
    {
        if (videoDisplay != null && enableVideoClickReplay)
        {
            // 确保有EventTrigger组件
            EventTrigger eventTrigger = videoDisplay.GetComponent<EventTrigger>();
            if (eventTrigger == null)
            {
                eventTrigger = videoDisplay.gameObject.AddComponent<EventTrigger>();
            }
            
            // 清除现有的事件
            eventTrigger.triggers.Clear();
            
            // 添加点击事件
            EventTrigger.Entry clickEntry = new EventTrigger.Entry();
            clickEntry.eventID = EventTriggerType.PointerClick;
            clickEntry.callback.AddListener((data) => {
                OnVideoClicked();
            });
            eventTrigger.triggers.Add(clickEntry);
            
            Debug.Log("已设置视频点击重播功能");
        }
    }
    
    // 视频点击事件处理
    private void OnVideoClicked()
    {
        if (enableVideoClickReplay && isVideoReady)
        {
            Debug.Log("用户点击视频，执行重播功能");
            
            // 执行与重播按钮相同的功能
            PlayCharacterSoundAndVideo();
            
            // 根据设置决定是否显示点击反馈效果
            if (enableClickFeedback)
            {
                StartCoroutine(VideoClickFeedback());
            }
        }
    }
    
    // 视频点击反馈效果
    private IEnumerator VideoClickFeedback()
    {
        if (videoDisplay != null)
        {
            // 保存原始颜色
            Color originalColor = videoDisplay.color;
            
            // 点击时稍微变亮而不是变暗
            Color clickColor = originalColor;
            clickColor.r = Mathf.Min(1f, clickColor.r * 1.2f);
            clickColor.g = Mathf.Min(1f, clickColor.g * 1.2f);
            clickColor.b = Mathf.Min(1f, clickColor.b * 1.2f);
            videoDisplay.color = clickColor;
            
            // 等待短暂时间
            yield return new WaitForSeconds(0.1f);
            
            // 恢复原始颜色
            videoDisplay.color = originalColor;
        }
    }
    
    private IEnumerator LoadVideoFromConfig()
    {
        // 重置视频状态
        isVideoReady = false;
        
        // 确保视频隐藏，加载指示器显示
        SetVideoDisplayVisible(false);
        ShowLoadingIndicator(true);
        
        if (string.IsNullOrEmpty(videoName) || string.IsNullOrEmpty(videoFolderPath))
        {
            Debug.LogError("视频名称或文件夹路径为空，无法加载视频");
            ShowLoadingIndicator(false);
            yield break;
        }
        
        // 构建完整的视频资源路径
        string fullVideoPath = $"{videoFolderPath}/{videoName}";
        Debug.Log($"尝试加载视频: {fullVideoPath}");
        
        // 尝试加载视频资源
        VideoClip videoClip = Resources.Load<VideoClip>(fullVideoPath);
        
        if (videoClip != null)
        {
            videoPlayer.clip = videoClip;
            Debug.Log($"视频资源加载成功: {fullVideoPath}");
            
            // 添加准备完成事件
            videoPlayer.prepareCompleted += OnVideoPrepared;
            
            // 预加载视频
            videoPlayer.Prepare();
            
            // 等待视频准备完成
            float prepareStartTime = Time.time;
            while (!videoPlayer.isPrepared && Time.time - prepareStartTime < videoLoadTimeout)
            {
                yield return null;
            }
            
            // 移除事件监听，避免重复触发
            videoPlayer.prepareCompleted -= OnVideoPrepared;
            
            if (videoPlayer.isPrepared)
            {
                Debug.Log("视频准备完成，可以播放");
                isVideoReady = true;
                
                // 隐藏加载指示器
                ShowLoadingIndicator(false);
                
                // 显示视频并设置第一帧
                SetVideoDisplayVisible(true);
                SetVideoFirstFrame();
                
                // 视频准备好后不自动播放，等待引导语播放完成后再播放
                // videoPlayer.Play();
            }
            else
            {
                Debug.LogWarning($"视频准备超时: {fullVideoPath}");
                ShowLoadingIndicator(false);
            }
        }
        else
        {
            // 详细的错误报告，帮助诊断问题
            Debug.LogError($"无法加载视频资源: {fullVideoPath}");
            Debug.LogError($"请检查以下可能的问题:");
            Debug.LogError($"1. 视频文件是否存在于 Assets/Resources/{fullVideoPath} 目录下");
            Debug.LogError($"2. 视频格式是否为Unity支持的格式(MP4, WebM)");
            Debug.LogError($"3. 视频文件是否已正确导入Unity(检查导入设置)");
            
            // 检查Resources文件夹中的可用资源
            UnityEngine.Object[] resources = Resources.LoadAll(videoFolderPath);
            if (resources.Length == 0)
            {
                Debug.LogError($"文件夹 '{videoFolderPath}' 中没有找到任何资源");
            }
            else
            {
                Debug.Log($"文件夹 '{videoFolderPath}' 中找到 {resources.Length} 个资源:");
                foreach (UnityEngine.Object res in resources)
                {
                    Debug.Log($" - {res.name} ({res.GetType()})");
                }
            }
            
            // 隐藏加载指示器
            ShowLoadingIndicator(false);
        }
    }
    
    // 视频准备完成的回调
    private void OnVideoPrepared(VideoPlayer vp)
    {
        Debug.Log("视频准备完成事件触发");
        isVideoReady = true;
        
        // 隐藏加载指示器
        ShowLoadingIndicator(false);
        
        // 显示视频并设置第一帧
        SetVideoDisplayVisible(true);
        SetVideoFirstFrame();
    }
    
    private void InitializeGame()
    {
        // 首先初始化视频，确保视频区域被隐藏，显示加载指示器
        InitializeVideo();

        // 然后初始化音频
        InitializeAudio();

        // 初始化汉字笔画渲染
        if (enableSVGRendering)
        {
            InitializeStrokeRendering();
        }

        // 如果启用按笔画播放模式，加载笔画数据
        if (enableStrokeByStrokeMode)
        {
            LoadStrokeData();
        }

        // 播放引导语音，视频会在引导语播放完成后自动播放
        PlayTutorialAndCharacterSound();
    }
    
    private void EnsureReplayButtonHasComponent()
    {
        if (replayButton != null)
        {
            // 确保按钮有LC_ReplaySoundBtn组件
            LC_ReplaySoundBtn replaySoundBtn = replayButton.GetComponent<LC_ReplaySoundBtn>();
            if (replaySoundBtn == null)
            {
                replaySoundBtn = replayButton.AddComponent<LC_ReplaySoundBtn>();
                Debug.Log("已为重新播放按钮添加LC_ReplaySoundBtn组件");
            }
            
            // 确保有Button组件
            Button button = replayButton.GetComponent<Button>();
            if (button == null)
            {
                button = replayButton.AddComponent<Button>();
                Debug.Log("已为重新播放按钮添加Button组件");
            }
        }
    }
    
    private void PlayTutorialAndCharacterSound()
    {
        StartCoroutine(PlayTutorialAndCharacterSoundCoroutine());
    }

    private IEnumerator PlayTutorialAndCharacterSoundCoroutine()
    {
        // 停止当前正在播放的所有音频
        StopAllAudio();
        
        // 先播放引导语音
        if (tutorial_sound != null && audioSource != null)
        {
            Debug.Log("播放引导语音");
            audioSource.clip = tutorial_sound;
            SoundManager.PlaySFX(tutorial_sound, false, 0);
            
            // 等待引导语音播放完成
            float tutorialDuration = tutorial_sound.length;
            yield return new WaitForSeconds(tutorialDuration - 0.8f); // 提前0.8秒结束等待
        }
        else
        {
            Debug.LogWarning("引导语音未设置");
            yield return null;
        }
        
        // 引导语播放完成后，播放汉字语音和视频
        Debug.Log("引导语播放完成，开始播放汉字语音和视频");
        PlayCharacterSound(currentCharacter);
        
        // 等待一小段时间后播放视频
        yield return new WaitForSeconds(0.5f);
        PlayWritingVideo();
    }
    
    private void StopAllAudio()
    {
        // 停止主音频源
        if (audioSource != null && audioSource.isPlaying)
        {
            audioSource.Stop();
        }
    }

    private void PlayCharacterSound(string character)
    {
        // 使用LC_LoadChineseData获取音频路径
        string characterSoundPath = LC_LoadChineseData.Instance.GetCharacterAudioPath(character);

        AudioClip sound = Resources.Load<AudioClip>(characterSoundPath);

        if (sound != null && audioSource != null)
        {
            // 停止当前播放的音频
            if (audioSource.isPlaying)
            {
                audioSource.Stop();
            }

            // 播放音频
            audioSource.clip = sound;
            SoundManager.PlaySFX(audioSource, sound, false, 0);
            // 确保音频能够播放
            if (!audioSource.isPlaying)
            {
                audioSource.Play();
            }

            Debug.Log($"播放汉字音频: {characterSoundPath}");
        }
        else
        {
            Debug.LogWarning($"无法加载汉字音频: {characterSoundPath}");
        }
    }
    
    // 播放汉字书写视频
    private void PlayWritingVideo()
    {
        // 如果启用SVG渲染，播放笔画动画
        if (enableSVGRendering && isStrokeRenderingInitialized)
        {
            Debug.Log("播放SVG笔画动画");
            PlayStrokeAnimation();
        }

        if (videoPlayer != null && videoPlayer.clip != null)
        {
            // 重置暂停状态
            isVideoPaused = false;

            // 检查是否启用按笔画播放模式
            if (enableStrokeByStrokeMode && strokeData != null)
            {
                Debug.Log("PlayWritingVideo: 启动按笔画播放模式");
                // 启动按笔画播放模式
                StartStrokeByStrokePlayback();
            }

            // 如果视频尚未准备好，显示加载指示器
            if (!isVideoReady)
            {
                // 确保视频隐藏，加载指示器显示
                SetVideoDisplayVisible(false);
                ShowLoadingIndicator(true);

                // 添加准备完成事件
                videoPlayer.prepareCompleted += (source) => {
                    Debug.Log("视频准备完成，开始播放");
                    ShowLoadingIndicator(false);
                    SetVideoDisplayVisible(true);
                    source.time = 0; // 确保从第一帧开始
                    source.Play();
                    isVideoPaused = false;

                    // 如果启用按笔画播放，开始监控
                    if (enableStrokeByStrokeMode && strokeData != null)
                    {
                        StartStrokeByStrokePlayback();
                    }

                    UpdatePausePlayButtonDisplay();
                };

                // 准备视频
                videoPlayer.Prepare();
                return;
            }

            // 停止当前播放的视频
            if (videoPlayer.isPlaying)
            {
                videoPlayer.Stop();
            }

            // 确保视频已准备好
            if (!videoPlayer.isPrepared)
            {
                Debug.Log("视频尚未准备好，正在准备...");

                // 确保视频隐藏，加载指示器显示
                SetVideoDisplayVisible(false);
                ShowLoadingIndicator(true);

                videoPlayer.Prepare();

                // 添加准备完成的事件监听
                videoPlayer.prepareCompleted += (source) => {
                    Debug.Log("视频准备完成，开始播放");
                    ShowLoadingIndicator(false);
                    SetVideoDisplayVisible(true);
                    source.time = 0; // 确保从第一帧开始
                    source.Play();
                    isVideoPaused = false;

                    // 如果启用按笔画播放，开始监控
                    if (enableStrokeByStrokeMode && strokeData != null)
                    {
                        StartStrokeByStrokePlayback();
                    }

                    UpdatePausePlayButtonDisplay();
                };
            }
            else
            {
                // 重置视频到开始位置并播放
                videoPlayer.time = 0;
                videoPlayer.Play();
                isVideoPaused = false;

                // 确保视频可见，加载指示器隐藏
                SetVideoDisplayVisible(true);
                ShowLoadingIndicator(false);

                // 如果启用按笔画播放，开始监控
                if (enableStrokeByStrokeMode && strokeData != null)
                {
                    StartStrokeByStrokePlayback();
                }

                // 更新按钮显示
                UpdatePausePlayButtonDisplay();

                Debug.Log($"播放汉字书写视频: {videoFolderPath}/{videoName}");
            }
        }
        else
        {
            Debug.LogWarning("视频播放器或视频资源未设置，尝试重新加载视频");
            if (LoadVideoConfig())
            {
                StartCoroutine(LoadVideoFromConfig());
            }
        }
    }

    // 设置视频第一帧作为占位符
    private void SetVideoFirstFrame()
    {
        if (videoPlayer != null && videoPlayer.clip != null && videoPlayer.isPrepared)
        {
            // 设置视频到第一帧
            videoPlayer.time = 0;

            // 暂停视频，只显示第一帧
            videoPlayer.Pause();

            Debug.Log("已设置视频第一帧作为占位符");
        }
    }

    // 视频播放完成事件处理
    private void OnVideoCompleted(VideoPlayer vp)
    {
        Debug.Log("视频播放完成事件触发");

        // 如果是按笔画播放模式，直接设置为重新开始状态
        if (enableStrokeByStrokeMode && strokeData != null)
        {
            Debug.Log("按笔画播放模式：视频播放完成，设置按钮为'重新开始'");

            // 直接设置完成状态
            isStrokeByStrokeActive = false;
            currentStrokeIndex = -1;
            isVideoPaused = false;

            // 停止视频进度监控
            if (videoProgressMonitor != null)
            {
                StopCoroutine(videoProgressMonitor);
                videoProgressMonitor = null;
            }

            // 调用UpdatePausePlayButtonDisplay来正确设置按钮状态
            UpdatePausePlayButtonDisplay();

            Debug.Log("视频播放完成，已调用UpdatePausePlayButtonDisplay");
        }
    }

    // 加载笔画数据
    private void LoadStrokeData()
    {
        try
        {
            // 构建笔画数据文件路径
            string strokeDataPath = $"ChineseChars/Strokes/{currentCharacter}";
            TextAsset strokeFile = Resources.Load<TextAsset>(strokeDataPath);

            if (strokeFile != null)
            {
                // 解析笔画数据
                strokeData = JsonUtility.FromJson<SimpleHanziData>(strokeFile.text);

                if (strokeData != null && strokeData.strokes != null && strokeData.strokes.Length > 0)
                {
                    // 计算笔画时间点
                    CalculateStrokeTimings();

                    Debug.Log($"成功加载笔画数据: {currentCharacter}, 共 {strokeData.strokes.Length} 个笔画");
                }
                else
                {
                    Debug.LogError($"笔画数据格式错误: {currentCharacter}");
                    strokeData = null;
                }
            }
            else
            {
                Debug.LogWarning($"未找到笔画数据文件: {strokeDataPath}");
                strokeData = null;
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"加载笔画数据时出错: {e.Message}");
            strokeData = null;
        }
    }

    // 计算每个笔画的时间点
    private void CalculateStrokeTimings()
    {
        if (strokeData == null || strokeData.strokes == null)
            return;

        int strokeCount = strokeData.strokes.Length;
        strokeTimings = new float[strokeCount];

        // 计算每个笔画的结束时间点（秒）
        for (int i = 0; i < strokeCount; i++)
        {
            // 第i个笔画的结束时间 = (i+1) * 笔画时间 + i * 间隔时间
            strokeTimings[i] = ((i + 1) * strokeDuration + i * delayBetweenStrokes) / 1000f;
        }

        Debug.Log($"计算笔画时间点完成，共 {strokeCount} 个笔画");
        for (int i = 0; i < strokeTimings.Length; i++)
        {
            Debug.Log($"笔画 {i + 1} 结束时间: {strokeTimings[i]:F2}秒");
        }
    }

    // 开始按笔画播放
    private void StartStrokeByStrokePlayback()
    {
        if (strokeData == null || strokeData.strokes == null || strokeData.strokes.Length == 0)
        {
            Debug.LogWarning("笔画数据无效，无法启动按笔画播放模式");
            return;
        }

        Debug.Log($"StartStrokeByStrokePlayback: 设置状态 - isStrokeByStrokeActive=true, currentStrokeIndex=0");

        isStrokeByStrokeActive = true;
        currentStrokeIndex = 0;
        isVideoPaused = false; // 确保开始时不是暂停状态

        // 在第一个笔画开始时播放汉字语音
        PlayCharacterSound(currentCharacter);

        // 开始监控视频进度
        if (videoProgressMonitor != null)
        {
            StopCoroutine(videoProgressMonitor);
        }
        videoProgressMonitor = StartCoroutine(MonitorVideoProgress());

        // 更新按钮显示（第一个笔画开始播放，按钮应该禁用）
        UpdatePausePlayButtonDisplay();

        Debug.Log($"开始按笔画播放模式，共 {strokeData.strokes.Length} 个笔画，监控协程已启动");
    }

    // 重新开始按笔画播放
    private void RestartStrokeByStrokePlayback()
    {
        Debug.Log("重新开始按笔画播放");

        // 重置状态
        currentStrokeIndex = 0;
        isVideoPaused = false;
        isStrokeByStrokeActive = false; // 先设为false，PlayWritingVideo会重新启动

        // 停止之前的监控协程
        if (videoProgressMonitor != null)
        {
            StopCoroutine(videoProgressMonitor);
            videoProgressMonitor = null;
        }

        // 确保视频停止
        if (videoPlayer != null)
        {
            videoPlayer.Stop();
        }

        // 重新播放视频和语音
        PlayWritingVideo();
    }

    // 停止按笔画播放模式
    private void StopStrokeByStrokeMode()
    {
        isStrokeByStrokeActive = false;
        currentStrokeIndex = 0;

        // 停止视频进度监控
        if (videoProgressMonitor != null)
        {
            StopCoroutine(videoProgressMonitor);
            videoProgressMonitor = null;
        }

        Debug.Log("已停止按笔画播放模式");
    }

    // 继续播放下一个笔画
    private void ContinueToNextStroke()
    {
        if (videoPlayer != null && isVideoReady)
        {
            // 继续播放视频
            videoPlayer.Play();
            isVideoPaused = false;

            // 重新开始监控视频进度
            if (videoProgressMonitor != null)
            {
                StopCoroutine(videoProgressMonitor);
            }
            videoProgressMonitor = StartCoroutine(MonitorVideoProgress());

            // 更新按钮显示
            UpdatePausePlayButtonDisplay();

            Debug.Log($"继续播放笔画 {currentStrokeIndex + 1}/{strokeData.strokes.Length}");
        }
    }

    // 完成按笔画播放
    private void CompleteStrokeByStrokePlayback()
    {
        Debug.Log("所有笔画播放完成");

        // 保持按笔画播放模式，但重置到完成状态
        isStrokeByStrokeActive = false; // 暂时停止监控
        currentStrokeIndex = -1; // 设置为完成状态

        // 停止视频进度监控
        if (videoProgressMonitor != null)
        {
            StopCoroutine(videoProgressMonitor);
            videoProgressMonitor = null;
        }

        Debug.Log($"设置currentStrokeIndex为: {currentStrokeIndex}");

        // 更新按钮显示为"重新开始"
        UpdatePausePlayButtonDisplay();

        Debug.Log("已调用UpdatePausePlayButtonDisplay，按钮应显示'重新开始'");
    }

    // 监控视频播放进度
    private IEnumerator MonitorVideoProgress()
    {
        while (videoPlayer != null && videoPlayer.isPlaying && isStrokeByStrokeActive)
        {
            float currentTime = (float)videoPlayer.time;

            // 检查是否到达当前笔画的结束时间
            if (currentStrokeIndex < strokeTimings.Length &&
                currentTime >= strokeTimings[currentStrokeIndex])
            {
                Debug.Log($"笔画 {currentStrokeIndex + 1} 播放完成，视频已暂停");

                // 检查是否是最后一个笔画
                if (currentStrokeIndex >= strokeData.strokes.Length - 1)
                {
                    // 最后一个笔画播放完成
                    Debug.Log("这是最后一个笔画，调用CompleteStrokeByStrokePlayback");
                    CompleteStrokeByStrokePlayback();
                }
                else
                {
                    // 不是最后一个笔画，暂停等待用户点击
                    videoPlayer.Pause();
                    isVideoPaused = true;

                    // 更新按钮显示
                    UpdatePausePlayButtonDisplay();
                }

                // 停止监控
                break;
            }

            yield return new WaitForSeconds(0.1f); // 每100ms检查一次
        }

        videoProgressMonitor = null;
    }

    // ==================== 轮廓渲染控制方法 ====================

    /// <summary>
    /// 设置轮廓渲染模式
    /// </summary>
    public void SetOutlineRenderingMode(bool enabled)
    {
        useOutlineRendering = enabled;

        // 更新所有现有的StrokeRenderer
        if (strokeRenderers != null)
        {
            foreach (var renderer in strokeRenderers)
            {
                if (renderer != null)
                {
                    renderer.SetOutlineRenderingMode(enabled);
                }
            }
        }

        Debug.Log($"轮廓渲染模式已{(enabled ? "启用" : "禁用")}");
    }

    /// <summary>
    /// 获取轮廓渲染模式状态
    /// </summary>
    public bool IsOutlineRenderingEnabled()
    {
        return useOutlineRendering;
    }

    /// <summary>
    /// 设置轮廓圆点大小
    /// </summary>
    public void SetOutlineDotSize(float size)
    {
        outlineDotSize = size;

        // 更新所有现有的StrokeRenderer
        if (strokeRenderers != null)
        {
            foreach (var renderer in strokeRenderers)
            {
                if (renderer != null)
                {
                    renderer.dotSize = size;
                }
            }
        }
    }

    /// <summary>
    /// 设置轮廓圆点间距
    /// </summary>
    public void SetOutlineDotSpacing(float spacing)
    {
        outlineDotSpacing = spacing;

        // 更新所有现有的StrokeRenderer
        if (strokeRenderers != null)
        {
            foreach (var renderer in strokeRenderers)
            {
                if (renderer != null)
                {
                    renderer.dotSpacing = spacing;
                }
            }
        }
    }

    /// <summary>
    /// 设置轮廓圆点颜色
    /// </summary>
    public void SetOutlineDotColor(Color color)
    {
        outlineDotColor = color;

        // 更新所有现有的StrokeRenderer
        if (strokeRenderers != null)
        {
            foreach (var renderer in strokeRenderers)
            {
                if (renderer != null)
                {
                    renderer.dotColor = color;
                }
            }
        }
    }
}

