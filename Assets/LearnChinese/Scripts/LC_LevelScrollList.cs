using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using DG.Tweening;

/// <summary>
/// 汉字学习关卡横向滚动列表管理器
/// 负责加载关卡数据并创建横向滚动的关卡卡片列表
/// </summary>
public class LC_LevelScrollList : MonoBehaviour
{
    [Header("UI组件")]
    [SerializeField] private ScrollRect scrollRect;
    [SerializeField] private Transform contentParent;
    [SerializeField] private GameObject levelCardPrefab; // 关卡卡片预制体
    
    [Header("滚动设置")]
    [SerializeField] private float cardSpacing = 20f; // 卡片间距
    [SerializeField] private float cardWidth = 300f; // 卡片宽度
    [SerializeField] private float scrollSpeed = 1000f; // 滚动速度
    
    [Header("数据设置")]
    [SerializeField] private string jsonFileName = "ChineseCharLevels-Phase1"; // JSON文件名
    
    [Header("音效")]
    [SerializeField] private AudioClip buttonClickSound;


    public List<Sprite> sprite_list1;
    
    // 私有变量
    private Dictionary<string, object> levelsData;
    private Dictionary<string, object> configData;
    private List<GameObject> levelCards = new List<GameObject>();
    private int totalLevels = 0;

    // 配置数据
    private List<Color> bgColors = new List<Color>();
    private List<string> levelIcons = new List<string>();
    
    void Start()
    {
        InitializeScrollList();
    }
    
    /// <summary>
    /// 初始化滚动列表
    /// </summary>
    private void InitializeScrollList()
    {
        // 加载关卡数据
        LoadLevelsData();
        
        // 创建关卡卡片
        CreateLevelCards();
        
        // 设置ScrollRect
        SetupScrollRect();
    }
    
    /// <summary>
    /// 加载关卡数据从JSON文件
    /// </summary>
    private void LoadLevelsData()
    {
        try
        {
            // 首先检查文件是否存在
            TextAsset jsonFile = Resources.Load<TextAsset>($"ChineseChars/{jsonFileName}");
            if (jsonFile == null)
            {
                Debug.LogError($"无法找到JSON文件: Resources/ChineseChars/{jsonFileName}.json");
                Debug.LogError("请确认文件路径是否正确，文件是否存在于Resources文件夹中");
                return;
            }

            string jsonString = jsonFile.text;
            if (string.IsNullOrEmpty(jsonString))
            {
                Debug.LogError($"JSON文件内容为空: {jsonFileName}");
                return;
            }

            // 解析JSON数据
            var data = MiniJsonExtensions.hashtableFromJson(jsonString);
            if (data == null)
            {
                Debug.LogError($"JSON解析失败: {jsonFileName}");
                return;
            }

            Debug.Log($"JSON解析成功，顶级字段数量: {data.Count}");
            foreach (var key in data.Keys)
            {
                Debug.Log($"顶级字段: {key}, 类型: {data[key]?.GetType()}");
            }

            // 解析config数据
            ParseConfigData(data);

            // 获取levels数据
            if (!data.ContainsKey("levels"))
            {
                Debug.LogError($"JSON文件中缺少'levels'字段: {jsonFileName}");
                return;
            }

            var levelsObject = data["levels"];
            Debug.Log($"levels字段类型: {levelsObject?.GetType()}");

            // MiniJsonExtensions通常返回Hashtable，需要转换
            if (levelsObject is System.Collections.Hashtable levelsHashtable)
            {
                levelsData = new Dictionary<string, object>();
                foreach (System.Collections.DictionaryEntry entry in levelsHashtable)
                {
                    levelsData[entry.Key.ToString()] = entry.Value;
                }
                Debug.Log($"成功转换levels数据，包含 {levelsData.Count} 个关卡");
            }
            else if (levelsObject is Dictionary<string, object> levelsDictionary)
            {
                levelsData = levelsDictionary;
                Debug.Log($"直接使用levels数据，包含 {levelsData.Count} 个关卡");
            }
            else
            {
                Debug.LogError($"'levels'字段格式不正确，实际类型: {levelsObject?.GetType()}, 文件: {jsonFileName}");
                return;
            }

            totalLevels = levelsData.Count;
            Debug.Log($"成功加载关卡数据，共{totalLevels}个关卡");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"加载关卡数据时出错: {e.Message}");
            Debug.LogError($"堆栈跟踪: {e.StackTrace}");
        }
    }

    /// <summary>
    /// 解析配置数据
    /// </summary>
    private void ParseConfigData(System.Collections.Hashtable data)
    {
        try
        {
            if (!data.ContainsKey("config"))
            {
                Debug.LogWarning("JSON中缺少config字段，使用默认配置");
                return;
            }

            var configObject = data["config"];

            // 处理config数据类型
            if (configObject is System.Collections.Hashtable configHashtable)
            {
                configData = new Dictionary<string, object>();
                foreach (System.Collections.DictionaryEntry entry in configHashtable)
                {
                    configData[entry.Key.ToString()] = entry.Value;
                }
            }
            else if (configObject is Dictionary<string, object> configDictionary)
            {
                configData = configDictionary;
            }

            if (configData == null)
            {
                Debug.LogWarning("config数据解析失败");
                return;
            }

            // 解析背景颜色
            ParseBackgroundColors();

            // 解析关卡图标
            ParseLevelIcons();

            Debug.Log($"配置数据解析完成: {bgColors.Count}个背景色, {levelIcons.Count}个图标");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"解析配置数据时出错: {e.Message}");
        }
    }

    /// <summary>
    /// 解析背景颜色
    /// </summary>
    private void ParseBackgroundColors()
    {
        bgColors.Clear();

        if (configData != null && configData.ContainsKey("bg_colors"))
        {
            var bgColorsObject = configData["bg_colors"];

            if (bgColorsObject is System.Collections.ArrayList bgColorsArray)
            {
                foreach (var colorItem in bgColorsArray)
                {
                    string colorString = colorItem.ToString();
                    Color color = ParseColorFromString(colorString);
                    bgColors.Add(color);
                }
            }
            else if (bgColorsObject is List<object> bgColorsList)
            {
                foreach (var colorItem in bgColorsList)
                {
                    string colorString = colorItem.ToString();
                    Color color = ParseColorFromString(colorString);
                    bgColors.Add(color);
                }
            }
        }

        // 如果没有配置颜色，使用默认颜色
        if (bgColors.Count == 0)
        {
            bgColors.Add(new Color(0.27f, 0.54f, 0.79f)); // 默认蓝色
            bgColors.Add(new Color(0.92f, 0.76f, 0.06f)); // 默认黄色
            bgColors.Add(new Color(0.44f, 0.34f, 0.80f)); // 默认紫色
        }

        Debug.Log($"解析了{bgColors.Count}个背景颜色");
    }

    /// <summary>
    /// 解析关卡图标
    /// </summary>
    private void ParseLevelIcons()
    {
        levelIcons.Clear();

        if (configData != null && configData.ContainsKey("level_icons"))
        {
            var levelIconsObject = configData["level_icons"];

            if (levelIconsObject is System.Collections.ArrayList levelIconsArray)
            {
                foreach (var iconItem in levelIconsArray)
                {
                    levelIcons.Add(iconItem.ToString());
                }
            }
            else if (levelIconsObject is List<object> levelIconsList)
            {
                foreach (var iconItem in levelIconsList)
                {
                    levelIcons.Add(iconItem.ToString());
                }
            }
        }

        Debug.Log($"解析了{levelIcons.Count}个关卡图标");
    }

    /// <summary>
    /// 从字符串解析颜色（使用GameUtility工具方法）
    /// </summary>
    private Color ParseColorFromString(string colorString)
    {
        try
        {
            // 将逗号分隔符转换为管道分隔符，以适配GameUtility.StringArrayToColor方法
            // 输入格式: "68, 138, 202" 或 "68,138,202"
            // 转换为: "68|138|202"
            string formattedColorString = colorString.Replace(",", "|").Replace(" ", "");

            Debug.Log($"颜色字符串转换: '{colorString}' -> '{formattedColorString}'");

            Color color = GameUtility.StringArrayToColor(formattedColorString);
            Debug.Log($"解析颜色结果: {color}");

            return color;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"解析颜色字符串失败: {colorString}, 错误: {e.Message}");
        }

        // 返回默认颜色
        return Color.white;
    }

    /// <summary>
    /// 获取指定关卡的背景颜色
    /// </summary>
    private Color GetBackgroundColorForLevel(int levelNumber)
    {
        if (bgColors.Count > 0)
        {
            // 循环使用背景颜色
            int colorIndex = (levelNumber - 1) % bgColors.Count;
            Color selectedColor = bgColors[colorIndex];
            Debug.Log($"关卡 {levelNumber}: 使用颜色索引 {colorIndex}, 颜色值 {selectedColor}");
            return selectedColor;
        }

        // 默认颜色
        Color defaultColor = new Color(0.27f, 0.54f, 0.79f);
        Debug.LogWarning($"关卡 {levelNumber}: 没有配置颜色，使用默认颜色 {defaultColor}");
        return defaultColor;
    }

    /// <summary>
    /// 获取指定关卡的图标精灵
    /// </summary>
    private Sprite GetIconSpriteForLevel(int levelNumber)
    {
        if (levelIcons.Count > 0 && sprite_list1 != null && sprite_list1.Count > 0)
        {
            // 循环使用图标
            int iconIndex = (levelNumber - 1) % levelIcons.Count;
            string iconName = levelIcons[iconIndex];

            Debug.Log($"关卡 {levelNumber}: 查找图标 '{iconName}' (索引 {iconIndex})");

            // 在sprite_list1中查找对应的精灵
            foreach (Sprite sprite in sprite_list1)
            {
                if (sprite != null && sprite.name == iconName)
                {
                    Debug.Log($"关卡 {levelNumber}: 找到匹配的精灵 '{iconName}'");
                    return sprite;
                }
            }

            Debug.LogWarning($"关卡 {levelNumber}: 未找到名为 '{iconName}' 的精灵");
            Debug.Log($"可用的精灵名称: {string.Join(", ", sprite_list1.Where(s => s != null).Select(s => s.name))}");
        }
        else
        {
            Debug.LogWarning($"关卡 {levelNumber}: levelIcons数量={levelIcons.Count}, sprite_list1数量={sprite_list1?.Count ?? 0}");
        }

        // 返回默认精灵或null
        Sprite defaultSprite = sprite_list1 != null && sprite_list1.Count > 0 ? sprite_list1[0] : null;
        Debug.Log($"关卡 {levelNumber}: 使用默认精灵 {defaultSprite?.name ?? "null"}");
        return defaultSprite;
    }
    
    /// <summary>
    /// 创建关卡卡片
    /// </summary>
    private void CreateLevelCards()
    {
        if (levelsData == null || levelCardPrefab == null || contentParent == null)
        {
            Debug.LogError("缺少必要的组件或数据");
            return;
        }
        
        // 清除现有卡片
        ClearExistingCards();
        
        // 为每个关卡创建卡片
        for (int i = 1; i <= totalLevels; i++)
        {
            string levelKey = i.ToString();
            if (levelsData.ContainsKey(levelKey))
            {
                var levelObject = levelsData[levelKey];
                Dictionary<string, object> levelData = null;

                // 处理不同的数据类型
                if (levelObject is System.Collections.Hashtable levelHashtable)
                {
                    levelData = new Dictionary<string, object>();
                    foreach (System.Collections.DictionaryEntry entry in levelHashtable)
                    {
                        levelData[entry.Key.ToString()] = entry.Value;
                    }
                }
                else if (levelObject is Dictionary<string, object> levelDictionary)
                {
                    levelData = levelDictionary;
                }

                if (levelData != null)
                {
                    CreateLevelCard(i, levelData);
                }
                else
                {
                    Debug.LogError($"关卡 {i} 数据格式不正确，类型: {levelObject?.GetType()}");
                }
            }
        }
        
        // 设置Content大小
        SetContentSize();
    }
    
    /// <summary>
    /// 创建单个关卡卡片
    /// </summary>
    private void CreateLevelCard(int levelNumber, Dictionary<string, object> levelData)
    {
        // 实例化卡片
        GameObject card = Instantiate(levelCardPrefab, contentParent);
        card.name = $"LevelCard_{levelNumber}";
        
        // 设置卡片位置
        RectTransform cardRect = card.GetComponent<RectTransform>();
        float xPosition = (levelNumber - 1) * (cardWidth + cardSpacing);
        cardRect.anchoredPosition = new Vector2(xPosition, 0);
        cardRect.sizeDelta = new Vector2(cardWidth, cardRect.sizeDelta.y);
        
        // 使用LC_LevelCard组件初始化卡片
        LC_LevelCard levelCard = card.GetComponent<LC_LevelCard>();
        if (levelCard != null)
        {
            // 获取背景颜色
            Color bgColor = GetBackgroundColorForLevel(levelNumber);

            // 获取图标精灵
            Sprite iconSprite = GetIconSpriteForLevel(levelNumber);

            // 初始化卡片
            levelCard.InitializeCard(levelNumber, levelData, bgColor, iconSprite);
        }
        else
        {
            Debug.LogWarning($"关卡卡片 {levelNumber} 缺少LC_LevelCard组件，使用备用方法");
            // 备用方法：直接设置数据
            SetupCardData(card, levelNumber, levelData);
        }

        // 添加点击事件
        Button cardButton = card.GetComponent<Button>();
        if (cardButton == null)
        {
            cardButton = card.AddComponent<Button>();
        }

        cardButton.onClick.RemoveAllListeners();
        cardButton.onClick.AddListener(() => OnLevelCardClicked(levelNumber, levelData));
        
        levelCards.Add(card);
    }
    
    /// <summary>
    /// 设置卡片数据显示
    /// </summary>
    private void SetupCardData(GameObject card, int levelNumber, Dictionary<string, object> levelData)
    {
        // 获取关卡中的汉字列表
        List<object> characters = null;

        if (levelData.ContainsKey("characters"))
        {
            var charactersObject = levelData["characters"];

            // 处理不同的数据类型
            if (charactersObject is List<object> charactersList)
            {
                characters = charactersList;
            }
            else if (charactersObject is System.Collections.ArrayList charactersArray)
            {
                characters = new List<object>();
                foreach (var item in charactersArray)
                {
                    characters.Add(item);
                }
            }
            else
            {
                Debug.LogError($"characters字段格式不正确，类型: {charactersObject?.GetType()}");
            }
        }
        
        // 设置关卡标题
        Transform titleTransform = card.transform.Find("Title");
        if (titleTransform != null)
        {
            TextMeshProUGUI titleText = titleTransform.GetComponent<TextMeshProUGUI>();
            if (titleText != null)
            {
                titleText.text = $"第{levelNumber}单元";
            }
        }

        // 设置汉字预览（显示前几个汉字）
        Transform charactersTransform = card.transform.Find("Characters");
        if (charactersTransform != null && characters != null)
        {
            TextMeshProUGUI charactersText = charactersTransform.GetComponent<TextMeshProUGUI>();
            if (charactersText != null)
            {
                string previewText = "";
                int maxPreview = Mathf.Min(5, characters.Count); // 最多显示5个汉字

                for (int i = 0; i < maxPreview; i++)
                {
                    previewText += characters[i].ToString();
                    if (i < maxPreview - 1) previewText += "";
                }

                charactersText.text = previewText;
            }
        }

        // 设置关卡进度或其他信息
        Transform progressTransform = card.transform.Find("Progress");
        if (progressTransform != null && characters != null)
        {
            TextMeshProUGUI progressText = progressTransform.GetComponent<TextMeshProUGUI>();
            if (progressText != null)
            {
                progressText.text = $"{characters.Count}个汉字";
            }
        }
    }
    
    /// <summary>
    /// 关卡卡片点击事件
    /// </summary>
    private void OnLevelCardClicked(int levelNumber, Dictionary<string, object> levelData)
    {
        // 播放点击音效
        if (buttonClickSound != null)
        {
            SoundManager.PlaySFX(buttonClickSound, false, 0);
        }
        
        // 卡片点击动画
        GameObject clickedCard = levelCards[levelNumber - 1];
        AnimateCardClick(clickedCard);
        
        Debug.Log($"点击了第{levelNumber}关卡");
        
        // 这里可以添加跳转到具体关卡的逻辑
        // 例如：跳转到学习场景，传递关卡数据
        StartCoroutine(LoadLevel(levelNumber, levelData));
    }
    
    /// <summary>
    /// 卡片点击动画
    /// </summary>
    private void AnimateCardClick(GameObject card)
    {
        if (card != null)
        {
            Vector3 originalScale = card.transform.localScale;
            
            // 创建点击动画序列
            Sequence clickSequence = DOTween.Sequence();
            clickSequence.Append(card.transform.DOScale(originalScale * 0.95f, 0.1f));
            clickSequence.Append(card.transform.DOScale(originalScale, 0.1f));
        }
    }
    
    /// <summary>
    /// 加载关卡（协程）
    /// </summary>
    private IEnumerator LoadLevel(int levelNumber, Dictionary<string, object> levelData)
    {
        yield return new WaitForSeconds(0.2f); // 等待动画完成
        
        // 这里添加具体的关卡加载逻辑
        // 例如：设置全局变量，跳转场景等
        Debug.Log($"开始加载第{levelNumber}关卡");
        
        // 示例：可以跳转到学习场景
        // GameUtility.GoToSceneName("LearnChinese_Xue");
    }
    
    /// <summary>
    /// 清除现有卡片
    /// </summary>
    private void ClearExistingCards()
    {
        foreach (GameObject card in levelCards)
        {
            if (card != null)
            {
                DestroyImmediate(card);
            }
        }
        levelCards.Clear();
    }
    
    /// <summary>
    /// 设置Content大小
    /// </summary>
    private void SetContentSize()
    {
        if (contentParent != null)
        {
            RectTransform contentRect = contentParent.GetComponent<RectTransform>();
            float totalWidth = totalLevels * cardWidth + (totalLevels - 1) * cardSpacing;
            contentRect.sizeDelta = new Vector2(totalWidth, contentRect.sizeDelta.y);
        }
    }
    
    /// <summary>
    /// 设置ScrollRect组件
    /// </summary>
    private void SetupScrollRect()
    {
        if (scrollRect != null)
        {
            scrollRect.horizontal = true;
            scrollRect.vertical = false;
            scrollRect.scrollSensitivity = scrollSpeed;
        }
    }
    
    /// <summary>
    /// 滚动到指定关卡
    /// </summary>
    public void ScrollToLevel(int levelNumber)
    {
        if (scrollRect != null && levelNumber > 0 && levelNumber <= totalLevels)
        {
            float normalizedPosition = (float)(levelNumber - 1) / (totalLevels - 1);
            scrollRect.horizontalNormalizedPosition = normalizedPosition;
        }
    }
    
    /// <summary>
    /// 获取关卡总数
    /// </summary>
    public int GetTotalLevels()
    {
        return totalLevels;
    }

    /// <summary>
    /// 测试颜色和图标配置（可在Inspector中右键调用）
    /// </summary>
    [ContextMenu("Test Color and Icon")]
    public void TestColorAndIcon()
    {
        Debug.Log("=== 开始测试颜色和图标配置 ===");

        // 测试颜色配置
        Debug.Log($"背景颜色数量: {bgColors.Count}");
        for (int i = 0; i < bgColors.Count; i++)
        {
            Debug.Log($"颜色 {i}: {bgColors[i]}");
        }

        // 测试图标配置
        Debug.Log($"图标名称数量: {levelIcons.Count}");
        for (int i = 0; i < levelIcons.Count; i++)
        {
            Debug.Log($"图标 {i}: {levelIcons[i]}");
        }

        // 测试精灵列表
        int spriteCount = sprite_list1 != null ? sprite_list1.Count : 0;
        Debug.Log($"精灵列表数量: {spriteCount}");
        if (sprite_list1 != null)
        {
            for (int i = 0; i < sprite_list1.Count; i++)
            {
                string spriteName = sprite_list1[i] != null ? sprite_list1[i].name : "null";
                Debug.Log($"精灵 {i}: {spriteName}");
            }
        }

        // 测试前5个关卡的颜色和图标分配
        Debug.Log("=== 关卡颜色和图标分配测试 ===");
        for (int i = 1; i <= 5; i++)
        {
            Color color = GetBackgroundColorForLevel(i);
            Sprite sprite = GetIconSpriteForLevel(i);
            string spriteName = sprite != null ? sprite.name : "null";
            Debug.Log($"关卡 {i}: 颜色 {color}, 图标 {spriteName}");
        }

        Debug.Log("=== 测试完成 ===");
    }
}
