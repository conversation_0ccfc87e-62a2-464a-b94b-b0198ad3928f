using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using DG.Tweening;

/// <summary>
/// 关卡卡片组件
/// 用于显示单个关卡的信息和处理交互
/// </summary>
public class LC_LevelCard : MonoBehaviour
{
    [Header("UI组件")]
    [SerializeField] private TextMeshProUGUI titleText;
    [SerializeField] private TextMeshProUGUI charactersText;
    [SerializeField] private TextMeshProUGUI progressText;
    [SerializeField] private Image backgroundImage;
    [SerializeField] private Image iconImage;
    
    [Header("视觉设置")]
    [SerializeField] private Color normalColor = Color.white;
    [SerializeField] private Color highlightColor = Color.yellow;
    [SerializeField] private float animationDuration = 0.2f;

    // 当前背景颜色（用于保存配置的颜色）
    private Color currentBackgroundColor = Color.white;

    
    // 私有变量
    private int levelNumber;
    private Dictionary<string, object> levelData;
    private Button cardButton;
    private bool isInitialized = false;
    
    void Awake()
    {
        // 获取Button组件
        cardButton = GetComponent<Button>();
        if (cardButton == null)
        {
            cardButton = gameObject.AddComponent<Button>();
        }
        
        // 自动查找子组件（如果没有在Inspector中设置）
        FindChildComponents();
    }
    
    /// <summary>
    /// 自动查找子组件
    /// </summary>
    private void FindChildComponents()
    {
        if (titleText == null)
            titleText = transform.Find("Title")?.GetComponent<TextMeshProUGUI>();
            
        if (charactersText == null)
            charactersText = transform.Find("Characters")?.GetComponent<TextMeshProUGUI>();
            
        if (progressText == null)
            progressText = transform.Find("Progress")?.GetComponent<TextMeshProUGUI>();
            
        if (backgroundImage == null)
            backgroundImage = GetComponent<Image>();
            
        if (iconImage == null)
            iconImage = transform.Find("Icon")?.GetComponent<Image>();
    }
    
    /// <summary>
    /// 初始化卡片数据
    /// </summary>
    public void InitializeCard(int level, Dictionary<string, object> data)
    {
        levelNumber = level;
        levelData = data;

        UpdateCardDisplay();
        isInitialized = true;
    }

    /// <summary>
    /// 初始化卡片数据（包含背景颜色和图标）
    /// </summary>
    public void InitializeCard(int level, Dictionary<string, object> data, Color backgroundColor, Sprite iconSprite)
    {
        levelNumber = level;
        levelData = data;

        string iconName = iconSprite != null ? iconSprite.name : "null";
        Debug.Log($"LC_LevelCard: 初始化关卡 {level}, 背景颜色 {backgroundColor}, 图标 {iconName}");

        // 保存并设置背景颜色
        currentBackgroundColor = backgroundColor;
        if (backgroundImage != null)
        {
            backgroundImage.color = backgroundColor;
            Debug.Log($"LC_LevelCard: 关卡 {level} 背景颜色已设置为 {backgroundColor}");
        }
        else
        {
            Debug.LogWarning($"LC_LevelCard: 关卡 {level} backgroundImage为null");
        }

        // 设置图标
        if (iconImage != null && iconSprite != null)
        {
            iconImage.sprite = iconSprite;
            iconImage.SetNativeSize();
            iconImage.gameObject.SetActive(true);
            Debug.Log($"LC_LevelCard: 关卡 {level} 图标已设置为 {iconSprite.name}");
        }
        else if (iconImage != null)
        {
            iconImage.gameObject.SetActive(false);
            Debug.LogWarning($"LC_LevelCard: 关卡 {level} 图标精灵为null，隐藏图标");
        }
        else
        {
            Debug.LogWarning($"LC_LevelCard: 关卡 {level} iconImage为null");
        }

        UpdateCardDisplay();
        isInitialized = true;
    }
    
    /// <summary>
    /// 更新卡片显示
    /// </summary>
    private void UpdateCardDisplay()
    {
        // 设置标题
        if (titleText != null)
        {
            titleText.text = $"第{levelNumber}单元";
        }
        
        // 设置汉字预览
        if (charactersText != null && levelData != null && levelData.ContainsKey("characters"))
        {
            List<object> characters = GetCharactersList();
            if (characters != null)
            {
                string previewText = "";
                int maxPreview = Mathf.Min(5, characters.Count);

                for (int i = 0; i < maxPreview; i++)
                {
                    previewText += characters[i].ToString();
                    if (i < maxPreview - 1) previewText += "";
                }

                charactersText.text = previewText;
            }
        }

        // 设置进度信息
        if (progressText != null && levelData != null && levelData.ContainsKey("characters"))
        {
            List<object> characters = GetCharactersList();
            if (characters != null)
            {
                progressText.text = $"{characters.Count}个汉字";
            }
        }
        
        // 设置背景颜色（使用配置的颜色或默认颜色）
        if (backgroundImage != null)
        {
            // 如果有配置的背景颜色，使用配置的颜色，否则使用默认颜色
            Color colorToUse = currentBackgroundColor;
            backgroundImage.color = colorToUse;
        }
    }

    /// <summary>
    /// 获取汉字列表（处理不同的数据类型）
    /// </summary>
    private List<object> GetCharactersList()
    {
        if (levelData == null || !levelData.ContainsKey("characters"))
        {
            return null;
        }

        var charactersObject = levelData["characters"];

        // 处理不同的数据类型
        if (charactersObject is List<object> charactersList)
        {
            return charactersList;
        }
        else if (charactersObject is System.Collections.ArrayList charactersArray)
        {
            List<object> characters = new List<object>();
            foreach (var item in charactersArray)
            {
                characters.Add(item);
            }
            return characters;
        }

        return null;
    }
    
    /// <summary>
    /// 播放点击动画
    /// </summary>
    public void PlayClickAnimation()
    {
        if (!isInitialized) return;
        
        // 缩放动画
        Vector3 originalScale = transform.localScale;
        Sequence clickSequence = DOTween.Sequence();
        clickSequence.Append(transform.DOScale(originalScale * 0.95f, animationDuration * 0.5f));
        clickSequence.Append(transform.DOScale(originalScale, animationDuration * 0.5f));
        
        // 颜色动画
        if (backgroundImage != null)
        {
            Color originalColor = currentBackgroundColor != Color.white ? currentBackgroundColor : normalColor;
            Sequence colorSequence = DOTween.Sequence();
            colorSequence.Append(backgroundImage.DOColor(highlightColor, animationDuration * 0.5f));
            colorSequence.Append(backgroundImage.DOColor(originalColor, animationDuration * 0.5f));
        }
    }
    
    /// <summary>
    /// 设置卡片高亮状态
    /// </summary>
    public void SetHighlight(bool highlight)
    {
        if (backgroundImage != null)
        {
            Color originalColor = currentBackgroundColor != Color.white ? currentBackgroundColor : normalColor;
            backgroundImage.color = highlight ? highlightColor : originalColor;
        }
    }
    
    /// <summary>
    /// 获取关卡编号
    /// </summary>
    public int GetLevelNumber()
    {
        return levelNumber;
    }
    
    /// <summary>
    /// 获取关卡数据
    /// </summary>
    public Dictionary<string, object> GetLevelData()
    {
        return levelData;
    }
    
    /// <summary>
    /// 检查是否已初始化
    /// </summary>
    public bool IsInitialized()
    {
        return isInitialized;
    }
}
