using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// 关卡滚动列表设置助手
/// 用于在编辑器中快速设置关卡滚动列表
/// </summary>
[System.Serializable]
public class LC_LevelScrollListSetup : MonoBehaviour
{
    [Header("自动设置")]
    [SerializeField] private bool autoSetup = false;
    
    [Header("组件引用")]
    [SerializeField] private ScrollRect scrollRect;
    [SerializeField] private Transform contentParent;
    [SerializeField] private GameObject levelCardTemplate;
    
    [Header("测试")]
    [SerializeField] private bool createTestCards = false;
    [SerializeField] private int testCardCount = 10;
    
    void Start()
    {
        if (autoSetup)
        {
            SetupScrollList();
        }
        
        if (createTestCards)
        {
            CreateTestCards();
        }
    }
    
    /// <summary>
    /// 自动设置滚动列表
    /// </summary>
    [ContextMenu("Setup Scroll List")]
    public void SetupScrollList()
    {
        // 查找组件
        if (scrollRect == null)
            scrollRect = GetComponent<ScrollRect>();
            
        if (contentParent == null)
            contentParent = transform.Find("Viewport/Content");
            
        if (scrollRect != null)
        {
            // 设置为横向滚动
            scrollRect.horizontal = true;
            scrollRect.vertical = false;
            
            Debug.Log("ScrollRect设置完成");
        }
        
        if (contentParent != null)
        {
            // 添加横向布局组件
            HorizontalLayoutGroup hlg = contentParent.GetComponent<HorizontalLayoutGroup>();
            if (hlg == null)
            {
                hlg = contentParent.gameObject.AddComponent<HorizontalLayoutGroup>();
            }
            hlg.spacing = 20f;
            hlg.childControlWidth = false;
            hlg.childControlHeight = false;
            hlg.childForceExpandWidth = false;
            hlg.childForceExpandHeight = false;
            
            // 添加内容大小适配器
            ContentSizeFitter csf = contentParent.GetComponent<ContentSizeFitter>();
            if (csf == null)
            {
                csf = contentParent.gameObject.AddComponent<ContentSizeFitter>();
            }
            csf.horizontalFit = ContentSizeFitter.FitMode.PreferredSize;
            csf.verticalFit = ContentSizeFitter.FitMode.Unconstrained;
            
            Debug.Log("Content布局设置完成");
        }
        
        // 设置LC_LevelScrollList组件
        LC_LevelScrollList levelScrollList = GetComponent<LC_LevelScrollList>();
        if (levelScrollList != null)
        {
            // 通过反射设置私有字段（仅用于设置）
            var scrollRectField = typeof(LC_LevelScrollList).GetField("scrollRect", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (scrollRectField != null)
                scrollRectField.SetValue(levelScrollList, scrollRect);
                
            var contentParentField = typeof(LC_LevelScrollList).GetField("contentParent", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (contentParentField != null)
                contentParentField.SetValue(levelScrollList, contentParent);
                
            Debug.Log("LC_LevelScrollList组件设置完成");
        }
    }
    
    /// <summary>
    /// 创建测试卡片
    /// </summary>
    [ContextMenu("Create Test Cards")]
    public void CreateTestCards()
    {
        if (contentParent == null || levelCardTemplate == null)
        {
            Debug.LogError("缺少必要的组件引用");
            return;
        }
        
        // 清除现有卡片
        for (int i = contentParent.childCount - 1; i >= 0; i--)
        {
            DestroyImmediate(contentParent.GetChild(i).gameObject);
        }
        
        // 创建测试卡片
        for (int i = 1; i <= testCardCount; i++)
        {
            GameObject card = Instantiate(levelCardTemplate, contentParent);
            card.name = $"TestCard_{i}";
            
            // 设置卡片大小
            RectTransform cardRect = card.GetComponent<RectTransform>();
            if (cardRect != null)
            {
                cardRect.sizeDelta = new Vector2(280, 180);
            }
            
            // 设置文本内容
            SetupCardText(card, i);
            
            // 添加按钮组件
            Button button = card.GetComponent<Button>();
            if (button == null)
            {
                button = card.AddComponent<Button>();
            }
            
            // 添加点击事件
            int levelNumber = i; // 捕获循环变量
            button.onClick.RemoveAllListeners();
            button.onClick.AddListener(() => {
                Debug.Log($"点击了第{levelNumber}关卡");
            });
        }
        
        Debug.Log($"创建了{testCardCount}个测试卡片");
    }
    
    /// <summary>
    /// 设置卡片文本内容
    /// </summary>
    private void SetupCardText(GameObject card, int levelNumber)
    {
        // 设置标题
        Transform titleTransform = card.transform.Find("Title");
        if (titleTransform != null)
        {
            TextMeshProUGUI titleText = titleTransform.GetComponent<TextMeshProUGUI>();
            if (titleText != null)
            {
                titleText.text = $"第{levelNumber}单元";
                titleText.fontSize = 24;
            }
        }
        
        // 设置汉字预览
        Transform charactersTransform = card.transform.Find("Characters");
        if (charactersTransform != null)
        {
            TextMeshProUGUI charactersText = charactersTransform.GetComponent<TextMeshProUGUI>();
            if (charactersText != null)
            {
                // 模拟汉字数据
                string[] sampleChars = {"一 二 三 四 五", "六 七 八 九 十", "人 口 目 耳 鼻", 
                                      "手 足 大 小 天", "上 下 左 右 中", "地 山 田 水 火",
                                      "木 土 日 月 是", "草 虫 鱼 鸟 马", "牛 羊 龙 车 书", "爸 妈 家 有 在"};
                
                int index = (levelNumber - 1) % sampleChars.Length;
                charactersText.text = sampleChars[index];
                charactersText.fontSize = 18;
            }
        }
        
        // 设置进度
        Transform progressTransform = card.transform.Find("Progress");
        if (progressTransform != null)
        {
            TextMeshProUGUI progressText = progressTransform.GetComponent<TextMeshProUGUI>();
            if (progressText != null)
            {
                progressText.text = "5个汉字";
                progressText.fontSize = 14;
            }
        }
    }
    
    /// <summary>
    /// 清除测试卡片
    /// </summary>
    [ContextMenu("Clear Test Cards")]
    public void ClearTestCards()
    {
        if (contentParent != null)
        {
            for (int i = contentParent.childCount - 1; i >= 0; i--)
            {
                DestroyImmediate(contentParent.GetChild(i).gameObject);
            }
            Debug.Log("已清除所有测试卡片");
        }
    }
}
