using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// 关卡滚动列表测试脚本
/// 用于测试关卡滚动列表的功能
/// </summary>
public class LC_LevelScrollListTest : MonoBehaviour
{
    [Header("测试设置")]
    [SerializeField] private LC_LevelScrollList levelScrollList;
    [SerializeField] private Button testButton;
    [SerializeField] private int targetLevel = 5;
    
    [Header("调试信息")]
    [SerializeField] private bool showDebugInfo = true;
    
    void Start()
    {
        // 查找组件
        if (levelScrollList == null)
            levelScrollList = FindObjectOfType<LC_LevelScrollList>();
            
        if (testButton == null)
            testButton = GameObject.Find("TestButton")?.GetComponent<Button>();
            
        // 设置测试按钮
        if (testButton != null)
        {
            testButton.onClick.RemoveAllListeners();
            testButton.onClick.AddListener(TestScrollToLevel);
        }
        
        // 显示调试信息
        if (showDebugInfo)
        {
            StartCoroutine(ShowDebugInfo());
        }
    }
    
    /// <summary>
    /// 测试滚动到指定关卡
    /// </summary>
    public void TestScrollToLevel()
    {
        if (levelScrollList != null)
        {
            levelScrollList.ScrollToLevel(targetLevel);
            Debug.Log($"滚动到第{targetLevel}关卡");
        }
        else
        {
            Debug.LogError("找不到LC_LevelScrollList组件");
        }
    }
    
    /// <summary>
    /// 显示调试信息
    /// </summary>
    private IEnumerator ShowDebugInfo()
    {
        yield return new WaitForSeconds(1f);
        
        if (levelScrollList != null)
        {
            int totalLevels = levelScrollList.GetTotalLevels();
            Debug.Log($"关卡滚动列表调试信息：");
            Debug.Log($"- 总关卡数: {totalLevels}");
            Debug.Log($"- 组件状态: 正常");
        }
        else
        {
            Debug.LogError("LC_LevelScrollList组件未找到或未正确设置");
        }
    }
    
    /// <summary>
    /// 在Inspector中显示的测试按钮
    /// </summary>
    [ContextMenu("Test Scroll To Level 1")]
    public void TestScrollToLevel1()
    {
        targetLevel = 1;
        TestScrollToLevel();
    }
    
    [ContextMenu("Test Scroll To Level 5")]
    public void TestScrollToLevel5()
    {
        targetLevel = 5;
        TestScrollToLevel();
    }
    
    [ContextMenu("Test Scroll To Level 10")]
    public void TestScrollToLevel10()
    {
        targetLevel = 10;
        TestScrollToLevel();
    }
}
