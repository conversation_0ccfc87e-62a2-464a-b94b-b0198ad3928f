using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// 简化版关卡滚动列表
/// 用于测试基本功能，不依赖JSON数据
/// </summary>
public class LC_LevelScrollListSimple : MonoBehaviour
{
    [Header("UI组件")]
    [SerializeField] private ScrollRect scrollRect;
    [SerializeField] private Transform contentParent;
    [SerializeField] private GameObject levelCardPrefab;
    
    [Header("测试设置")]
    [SerializeField] private int testLevelCount = 10;
    [SerializeField] private float cardSpacing = 20f;
    [SerializeField] private float cardWidth = 300f;
    
    [Header("音效")]
    [SerializeField] private AudioClip buttonClickSound;
    
    private List<GameObject> levelCards = new List<GameObject>();
    
    void Start()
    {
        InitializeScrollList();
    }
    
    /// <summary>
    /// 初始化滚动列表
    /// </summary>
    private void InitializeScrollList()
    {
        Debug.Log("开始初始化简化版关卡滚动列表");
        
        // 检查必要组件
        if (!ValidateComponents())
        {
            return;
        }
        
        // 设置ScrollRect
        SetupScrollRect();
        
        // 创建测试关卡卡片
        CreateTestLevelCards();
        
        Debug.Log($"简化版关卡滚动列表初始化完成，创建了{testLevelCount}个测试卡片");
    }
    
    /// <summary>
    /// 验证必要组件
    /// </summary>
    private bool ValidateComponents()
    {
        if (scrollRect == null)
        {
            scrollRect = GetComponent<ScrollRect>();
            if (scrollRect == null)
            {
                Debug.LogError("找不到ScrollRect组件");
                return false;
            }
        }
        
        if (contentParent == null)
        {
            Transform viewport = transform.Find("Viewport");
            if (viewport != null)
            {
                contentParent = viewport.Find("Content");
            }
            
            if (contentParent == null)
            {
                Debug.LogError("找不到Content容器");
                return false;
            }
        }
        
        if (levelCardPrefab == null)
        {
            Debug.LogError("未设置关卡卡片预制体");
            return false;
        }
        
        Debug.Log("组件验证通过");
        return true;
    }
    
    /// <summary>
    /// 设置ScrollRect组件
    /// </summary>
    private void SetupScrollRect()
    {
        scrollRect.horizontal = true;
        scrollRect.vertical = false;
        
        // 设置Content的布局组件
        HorizontalLayoutGroup hlg = contentParent.GetComponent<HorizontalLayoutGroup>();
        if (hlg == null)
        {
            hlg = contentParent.gameObject.AddComponent<HorizontalLayoutGroup>();
        }
        hlg.spacing = cardSpacing;
        hlg.childControlWidth = false;
        hlg.childControlHeight = false;
        
        // 设置ContentSizeFitter
        ContentSizeFitter csf = contentParent.GetComponent<ContentSizeFitter>();
        if (csf == null)
        {
            csf = contentParent.gameObject.AddComponent<ContentSizeFitter>();
        }
        csf.horizontalFit = ContentSizeFitter.FitMode.PreferredSize;
        csf.verticalFit = ContentSizeFitter.FitMode.Unconstrained;
        
        Debug.Log("ScrollRect设置完成");
    }
    
    /// <summary>
    /// 创建测试关卡卡片
    /// </summary>
    private void CreateTestLevelCards()
    {
        // 清除现有卡片
        ClearExistingCards();
        
        // 测试数据
        string[] testCharacters = {
            "一 二 三 四 五",
            "六 七 八 九 十", 
            "人 口 目 耳 鼻",
            "手 足 大 小 天",
            "上 下 左 右 中",
            "地 山 田 水 火",
            "木 土 日 月 是",
            "草 虫 鱼 鸟 马",
            "牛 羊 龙 车 书",
            "爸 妈 家 有 在"
        };
        
        for (int i = 1; i <= testLevelCount; i++)
        {
            CreateTestLevelCard(i, testCharacters);
        }
        
        Debug.Log($"创建了{testLevelCount}个测试卡片");
    }
    
    /// <summary>
    /// 创建单个测试关卡卡片
    /// </summary>
    private void CreateTestLevelCard(int levelNumber, string[] testCharacters)
    {
        // 实例化卡片
        GameObject card = Instantiate(levelCardPrefab, contentParent);
        card.name = $"TestLevelCard_{levelNumber}";
        
        // 设置卡片大小
        RectTransform cardRect = card.GetComponent<RectTransform>();
        if (cardRect != null)
        {
            cardRect.sizeDelta = new Vector2(cardWidth, cardRect.sizeDelta.y);
        }
        
        // 设置卡片数据
        SetupTestCardData(card, levelNumber, testCharacters);
        
        // 添加点击事件
        Button cardButton = card.GetComponent<Button>();
        if (cardButton == null)
        {
            cardButton = card.AddComponent<Button>();
        }
        
        cardButton.onClick.RemoveAllListeners();
        cardButton.onClick.AddListener(() => OnTestLevelCardClicked(levelNumber));
        
        levelCards.Add(card);
    }
    
    /// <summary>
    /// 设置测试卡片数据
    /// </summary>
    private void SetupTestCardData(GameObject card, int levelNumber, string[] testCharacters)
    {
        // 设置标题
        Transform titleTransform = card.transform.Find("Title");
        if (titleTransform != null)
        {
            TextMeshProUGUI titleText = titleTransform.GetComponent<TextMeshProUGUI>();
            if (titleText != null)
            {
                titleText.text = $"第{levelNumber}单元";
            }
        }
        
        // 设置汉字预览
        Transform charactersTransform = card.transform.Find("Characters");
        if (charactersTransform != null)
        {
            TextMeshProUGUI charactersText = charactersTransform.GetComponent<TextMeshProUGUI>();
            if (charactersText != null)
            {
                int index = (levelNumber - 1) % testCharacters.Length;
                charactersText.text = testCharacters[index];
            }
        }
        
        // 设置进度
        Transform progressTransform = card.transform.Find("Progress");
        if (progressTransform != null)
        {
            TextMeshProUGUI progressText = progressTransform.GetComponent<TextMeshProUGUI>();
            if (progressText != null)
            {
                progressText.text = "5个汉字";
            }
        }
    }
    
    /// <summary>
    /// 测试关卡卡片点击事件
    /// </summary>
    private void OnTestLevelCardClicked(int levelNumber)
    {
        // 播放点击音效
        if (buttonClickSound != null)
        {
            SoundManager.PlaySFX(buttonClickSound, false, 0);
        }
        
        Debug.Log($"点击了测试关卡 {levelNumber}");
        
        // 这里可以添加跳转逻辑
    }
    
    /// <summary>
    /// 清除现有卡片
    /// </summary>
    private void ClearExistingCards()
    {
        foreach (GameObject card in levelCards)
        {
            if (card != null)
            {
                DestroyImmediate(card);
            }
        }
        levelCards.Clear();
    }
    
    /// <summary>
    /// 滚动到指定关卡
    /// </summary>
    public void ScrollToLevel(int levelNumber)
    {
        if (scrollRect != null && levelNumber > 0 && levelNumber <= testLevelCount)
        {
            float normalizedPosition = (float)(levelNumber - 1) / (testLevelCount - 1);
            scrollRect.horizontalNormalizedPosition = normalizedPosition;
        }
    }
}
