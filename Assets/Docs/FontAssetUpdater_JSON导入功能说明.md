# FontAssetUpdater JSON导入功能说明

## 功能概述

FontAssetUpdater工具新增了从JSON文件批量导入汉字的功能，可以通过拖拽方式添加多个JSON文件，自动提取其中的汉字并添加到TextMeshPro字体资源中。

## 主要功能

### 1. 字体资源健康检查
- 自动检测字体资源状态
- 识别图集纹理丢失、材质丢失等问题
- 提供自动修复功能
- 显示详细的问题诊断信息

### 2. 拖拽导入JSON文件
- 支持同时拖拽多个JSON文件到工具界面
- 自动识别.json文件格式
- 显示已添加的文件列表
- 支持单独移除不需要的文件

### 3. 汉字提取选项
- **从词语中提取汉字**: 提取JSON中`learning_content.word.text`字段的汉字
- **从句子中提取汉字**: 提取JSON中`learning_content.sentence.text`字段的汉字
- 自动去重，避免重复字符
- 只提取中文字符（Unicode范围：0x4E00-0x9FFF）

### 4. 字符预览和管理
- 实时显示提取到的汉字数量
- 可预览所有提取的汉字
- 一键添加到字符列表
- 支持清空操作

## 使用方法

### 步骤1：打开工具
在Unity编辑器中，选择菜单：`Tools > TextMeshPro > Font Asset Updater`

### 步骤2：展开JSON导入功能
点击"从JSON文件导入汉字"折叠面板

### 步骤3：添加JSON文件
将JSON文件从项目窗口或文件资源管理器拖拽到"拖拽JSON文件到这里"区域

### 步骤4：设置提取选项
- 勾选"从词语中提取汉字"：提取词语中的汉字
- 勾选"从句子中提取汉字"：提取句子中的汉字

### 步骤5：提取汉字
点击"提取汉字"按钮，工具会自动解析所有JSON文件并提取汉字

### 步骤6：添加到字符列表
点击"添加到字符列表"按钮，将提取的汉字添加到工具的字符输入框中

### 步骤7：更新字体资源
选择目标字体资源，点击"添加字符到字体资源"完成更新

## 🔧 字体资源问题修复

### 常见问题：字符显示空白
如果遇到`MissingReferenceException: The variable m_AtlasTextures of TMP_FontAsset doesn't exist anymore`错误：

1. **选择字体资源**：在工具中选择有问题的字体资源
2. **查看健康检查**：工具会自动显示字体资源的健康状态
3. **自动修复**：如果显示错误信息，点击"尝试自动修复"
4. **手动修复**：如果自动修复失败，使用"重新生成字体资源"功能

### 修复步骤详解
1. 打开FontAssetUpdater工具
2. 选择损坏的字体资源（如`SourceHanSerifSC-VF SDF.asset`）
3. 工具会显示具体的问题（图集纹理丢失、材质丢失等）
4. 在字符列表中添加需要的汉字
5. 展开"高级设置"，点击"重新生成字体资源"
6. 新的健康字体资源会保存为`原文件名_Regenerated.asset`

## 支持的JSON格式

工具支持以下JSON结构格式：

```json
{
  "config": {
    "version": "1.0",
    "total_chars": 50,
    "difficulty_level": "beginner"
  },
  "characters": {
    "一": {
      "character": {
        "pinyin": "yī",
        "tone": 1,
        "strokes": 1,
        "radical": "一"
      },
      "learning_content": {
        "word": {
          "text": "一个",
          "pinyin": "yī gè",
          "meaning": "一个物品"
        },
        "sentence": {
          "text": "我有一个苹果。",
          "pinyin": "wǒ yǒu yí gè píng guǒ",
          "meaning": "我有一个苹果"
        }
      }
    }
  }
}
```

## 注意事项

1. **文件格式**: 只支持.json格式的文件
2. **JSON结构**: 必须包含`characters`根节点
3. **字符提取**: 只提取中文字符，忽略标点符号和其他字符
4. **去重处理**: 自动去除重复的汉字
5. **错误处理**: 如果JSON格式错误，会在控制台显示错误信息

## 技术实现

### 核心类和方法

- `DrawJsonImportSection()`: 绘制JSON导入界面
- `HandleDragAndDrop()`: 处理文件拖拽操作
- `ExtractCharactersFromJsonFiles()`: 批量处理JSON文件
- `ExtractCharactersFromJson()`: 解析单个JSON文件
- `ExtractChineseCharacters()`: 提取中文字符
- `IsChineseCharacter()`: 判断是否为中文字符

### 依赖项

- `Newtonsoft.Json.Linq`: 用于JSON解析
- `UnityEditor`: Unity编辑器API
- `TMPro`: TextMeshPro字体系统

## 更新日志

### v1.1.0 (当前版本)
- 新增JSON文件拖拽导入功能
- 新增汉字自动提取功能
- 新增提取选项配置
- 新增字符预览功能
- 优化用户界面布局

### v1.0.0 (原始版本)
- 基础字体资源更新功能
- Unicode字符查询功能
- 自定义字符列表功能
