using UnityEngine;
using UnityEditor;
using TMPro;
using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEngine.TextCore.LowLevel;
using System.Linq;
using System.Globalization;

public class FontAssetUpdater : EditorWindow
{
    private TMP_FontAsset fontAsset;
    private string charactersToAdd = "流"; // 默认添加"流"字符
    private bool useCustomCharacterList = false;
    private bool showPreview = true;
    private int atlasWidth = 1024;
    private int atlasHeight = 1024;
    private int atlasPadding = 5;
    private GlyphRenderMode renderMode = GlyphRenderMode.SDFAA;
    private bool showAdvancedOptions = false;
    private bool showUnicodeQuery = false;
    private string unicodeInput = "6811";
    private string unicodeQueryResult = "";

    [MenuItem("Tools/TextMeshPro/Font Asset Updater")]
    public static void ShowWindow()
    {
        GetWindow<FontAssetUpdater>("Font Asset Updater");
    }

    void OnGUI()
    {
        GUILayout.Label("TextMeshPro 字体资源更新工具", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        // Unicode查询工具
        showUnicodeQuery = EditorGUILayout.Foldout(showUnicodeQuery, "Unicode字符查询");
        if (showUnicodeQuery)
        {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.PrefixLabel("Unicode值 (不含\\u)");
            unicodeInput = EditorGUILayout.TextField(unicodeInput);
            if (GUILayout.Button("查询", GUILayout.Width(60)))
            {
                QueryUnicode();
            }
            EditorGUILayout.EndHorizontal();

            if (!string.IsNullOrEmpty(unicodeQueryResult))
            {
                EditorGUILayout.HelpBox(unicodeQueryResult, MessageType.Info);
                
                if (GUILayout.Button("添加到字符列表"))
                {
                    if (TryGetCharFromUnicode(unicodeInput, out char c))
                    {
                        charactersToAdd += c;
                        useCustomCharacterList = true;
                    }
                }
            }
            
            EditorGUILayout.Space();
        }

        fontAsset = EditorGUILayout.ObjectField("字体资源", fontAsset, typeof(TMP_FontAsset), false) as TMP_FontAsset;
        
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("字符添加选项", EditorStyles.boldLabel);
        
        useCustomCharacterList = EditorGUILayout.Toggle("使用自定义字符列表", useCustomCharacterList);
        
        if (useCustomCharacterList)
        {
            EditorGUILayout.HelpBox("输入需要添加的字符", MessageType.Info);
            charactersToAdd = EditorGUILayout.TextArea(charactersToAdd, GUILayout.Height(100));
        }
        else
        {
            EditorGUILayout.HelpBox("将添加默认字符: 流", MessageType.Info);
        }

        EditorGUILayout.Space();
        showAdvancedOptions = EditorGUILayout.Foldout(showAdvancedOptions, "高级设置");
        if (showAdvancedOptions)
        {
            EditorGUILayout.LabelField("图集设置", EditorStyles.boldLabel);
            atlasWidth = EditorGUILayout.IntPopup("图集宽度", atlasWidth, new string[] { "32", "64", "128", "256", "512", "1024", "2048", "4096", "8192" }, new int[] { 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192 });
            atlasHeight = EditorGUILayout.IntPopup("图集高度", atlasHeight, new string[] { "32", "64", "128", "256", "512", "1024", "2048", "4096", "8192" }, new int[] { 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192 });
            atlasPadding = EditorGUILayout.IntSlider("图集内边距", atlasPadding, 1, 10);
            renderMode = (GlyphRenderMode)EditorGUILayout.EnumPopup("渲染模式", renderMode);
        }

        showPreview = EditorGUILayout.Foldout(showPreview, "字符预览");
        if (showPreview)
        {
            EditorGUILayout.LabelField("将添加的字符:");
            EditorGUILayout.SelectableLabel(charactersToAdd, EditorStyles.textField, GUILayout.Height(50));
            
            if (!string.IsNullOrEmpty(charactersToAdd))
            {
                EditorGUILayout.LabelField("Unicode 值:");
                string unicodeValues = GetUnicodeValues(charactersToAdd);
                EditorGUILayout.SelectableLabel(unicodeValues, EditorStyles.textField, GUILayout.Height(50));
            }
        }

        EditorGUILayout.Space();
        
        GUI.enabled = fontAsset != null && !string.IsNullOrEmpty(charactersToAdd);
        if (GUILayout.Button("添加字符到字体资源"))
        {
            AddCharactersToFontAsset();
        }

        if (fontAsset != null && !string.IsNullOrEmpty(charactersToAdd) && showAdvancedOptions)
        {
            if (GUILayout.Button("重新生成字体资源"))
            {
                RegenerateFontAsset();
            }
        }
        GUI.enabled = true;
    }

    private void QueryUnicode()
    {
        if (string.IsNullOrEmpty(unicodeInput))
        {
            unicodeQueryResult = "请输入有效的Unicode值";
            return;
        }

        if (TryGetCharFromUnicode(unicodeInput, out char character))
        {
            unicodeQueryResult = $"Unicode \\u{unicodeInput} 对应的字符是: {character}\n" +
                               $"字符名称: {GetCharacterName(character)}";
        }
        else
        {
            unicodeQueryResult = "无效的Unicode值，请使用有效的十六进制值";
        }
    }

    private bool TryGetCharFromUnicode(string unicodeHex, out char character)
    {
        character = '\0';
        try
        {
            if (int.TryParse(unicodeHex, NumberStyles.HexNumber, CultureInfo.InvariantCulture, out int unicodeValue))
            {
                if (unicodeValue >= 0 && unicodeValue <= 0xFFFF)
                {
                    character = (char)unicodeValue;
                    return true;
                }
            }
        }
        catch
        {
            // 解析失败
        }
        return false;
    }

    private string GetCharacterName(char c)
    {
        // 返回常见中文字符的名称
        switch (c)
        {
            case '树': return "树 (shù) - 树木、树林的'树'";
            case '松': return "松 (sōng) - 松树的'松'";
            case '流': return "流 (liú) - 流水的'流'";
            default:
                if (c >= 0x4E00 && c <= 0x9FFF)
                    return "汉字";
                else
                    return "未知字符";
        }
    }

    private void AddCharactersToFontAsset()
    {
        if (fontAsset == null || string.IsNullOrEmpty(charactersToAdd))
            return;

        try
        {
            // 去重处理
            HashSet<uint> uniqueCharacters = new HashSet<uint>();
            foreach (char c in charactersToAdd)
            {
                uniqueCharacters.Add(c);
            }
            
            // 创建字符集
            uint[] characterSet = new uint[uniqueCharacters.Count];
            int index = 0;
            foreach (uint c in uniqueCharacters)
            {
                characterSet[index++] = c;
            }
            
            // 使用TMP_FontAsset.TryAddCharacters方法
            bool result = fontAsset.TryAddCharacters(characterSet);
            
            // 标记资源为已修改并保存
            EditorUtility.SetDirty(fontAsset);
            AssetDatabase.SaveAssets();
            
            if (result)
            {
                Debug.Log($"成功添加字符到字体资源 {fontAsset.name}");
                EditorUtility.DisplayDialog("成功", "已成功添加字符到字体资源", "确定");
            }
            else
            {
                Debug.LogWarning($"部分或全部字符无法添加到字体资源 {fontAsset.name}");
                EditorUtility.DisplayDialog("警告", "部分或全部字符无法添加到字体资源。可能需要重新生成字体资源。", "确定");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"添加字符时出错: {e.Message}");
            EditorUtility.DisplayDialog("错误", $"添加字符时出错: {e.Message}", "确定");
        }
    }

    private void RegenerateFontAsset()
    {
        if (fontAsset == null || string.IsNullOrEmpty(charactersToAdd))
            return;

        try
        {
            // 获取源字体
            Font sourceFont = fontAsset.sourceFontFile;
            if (sourceFont == null)
            {
                EditorUtility.DisplayDialog("错误", "无法获取源字体文件，请确保字体资源包含源字体引用", "确定");
                return;
            }

            // 去重处理
            HashSet<uint> uniqueCharacters = new HashSet<uint>();
            foreach (char c in charactersToAdd)
            {
                uniqueCharacters.Add(c);
            }

            // 创建字符集
            string characterSequence = new string(charactersToAdd.ToCharArray().Distinct().ToArray());
            
            Debug.Log("开始重新生成字体资源...");
            
            // 创建新的字体资源
            TMP_FontAsset newFontAsset = TMP_FontAsset.CreateFontAsset(
                sourceFont, 
                fontAsset.faceInfo.pointSize, 
                atlasPadding, 
                renderMode, 
                atlasWidth, 
                atlasHeight);
            
            if (newFontAsset != null)
            {
                // 添加字符
                uint[] characterSet = new uint[characterSequence.Length];
                for (int i = 0; i < characterSequence.Length; i++)
                {
                    characterSet[i] = characterSequence[i];
                }
                
                // 尝试添加字符
                bool addResult = newFontAsset.TryAddCharacters(characterSet);
                
                if (!addResult)
                {
                    Debug.LogWarning("部分字符可能无法添加");
                }
                
                // 保存字体资源
                string assetPath = AssetDatabase.GetAssetPath(fontAsset);
                string directory = Path.GetDirectoryName(assetPath);
                string fileName = Path.GetFileNameWithoutExtension(assetPath) + "_Regenerated.asset";
                string fullOutputPath = Path.Combine(directory, fileName);
                
                AssetDatabase.CreateAsset(newFontAsset, fullOutputPath);
                
                // 保存材质
                Material material = new Material(Shader.Find("TextMeshPro/Distance Field"));
                material.SetTexture("_MainTex", newFontAsset.atlasTexture);
                material.SetFloat("_TextureWidth", newFontAsset.atlasWidth);
                material.SetFloat("_TextureHeight", newFontAsset.atlasHeight);
                
                AssetDatabase.AddObjectToAsset(material, newFontAsset);
                newFontAsset.material = material;
                
                EditorUtility.SetDirty(newFontAsset);
                AssetDatabase.SaveAssets();
                
                Debug.Log($"字体资源已重新生成并保存到: {fullOutputPath}");
                EditorUtility.DisplayDialog("成功", $"字体资源已成功重新生成\n保存路径: {fullOutputPath}", "确定");
            }
            else
            {
                Debug.LogError("无法创建字体资源");
                EditorUtility.DisplayDialog("错误", "无法创建字体资源", "确定");
            }
            
            // 刷新资源数据库
            AssetDatabase.Refresh();
        }
        catch (System.Exception e)
        {
            Debug.LogError($"重新生成字体资源时出错: {e.Message}");
            EditorUtility.DisplayDialog("错误", $"重新生成字体资源时出错: {e.Message}", "确定");
        }
    }

    private string GetUnicodeValues(string text)
    {
        if (string.IsNullOrEmpty(text))
            return "";

        StringBuilder sb = new StringBuilder();
        foreach (char c in text)
        {
            sb.AppendLine($"\\u{(int)c:X4} ({c})");
        }
        return sb.ToString();
    }
} 