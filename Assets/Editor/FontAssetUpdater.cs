using UnityEngine;
using UnityEditor;
using TMPro;
using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEngine.TextCore.LowLevel;
using System.Linq;
using System.Globalization;
using Newtonsoft.Json.Linq;

public class FontAssetUpdater : EditorWindow
{
    private TMP_FontAsset fontAsset;
    private string charactersToAdd = "流"; // 默认添加"流"字符
    private bool useCustomCharacterList = false;
    private bool showPreview = true;
    private int atlasWidth = 1024;
    private int atlasHeight = 1024;
    private int atlasPadding = 5;
    private GlyphRenderMode renderMode = GlyphRenderMode.SDFAA;
    private bool showAdvancedOptions = false;
    private bool showUnicodeQuery = false;
    private string unicodeInput = "6811";
    private string unicodeQueryResult = "";

    // JSON文件相关字段
    private bool showJsonImport = false;
    private List<string> jsonFilePaths = new List<string>();
    private Vector2 jsonScrollPosition;
    private bool extractFromWords = true;
    private bool extractFromSentences = true;
    private HashSet<char> extractedCharacters = new HashSet<char>();

    [MenuItem("Tools/TextMeshPro/Font Asset Updater")]
    public static void ShowWindow()
    {
        GetWindow<FontAssetUpdater>("Font Asset Updater");
    }

    void OnGUI()
    {
        GUILayout.Label("TextMeshPro 字体资源更新工具", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        // Unicode查询工具
        showUnicodeQuery = EditorGUILayout.Foldout(showUnicodeQuery, "Unicode字符查询");
        if (showUnicodeQuery)
        {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.PrefixLabel("Unicode值 (不含\\u)");
            unicodeInput = EditorGUILayout.TextField(unicodeInput);
            if (GUILayout.Button("查询", GUILayout.Width(60)))
            {
                QueryUnicode();
            }
            EditorGUILayout.EndHorizontal();

            if (!string.IsNullOrEmpty(unicodeQueryResult))
            {
                EditorGUILayout.HelpBox(unicodeQueryResult, MessageType.Info);
                
                if (GUILayout.Button("添加到字符列表"))
                {
                    if (TryGetCharFromUnicode(unicodeInput, out char c))
                    {
                        charactersToAdd += c;
                        useCustomCharacterList = true;
                    }
                }
            }
            
            EditorGUILayout.Space();
        }

        fontAsset = EditorGUILayout.ObjectField("字体资源", fontAsset, typeof(TMP_FontAsset), false) as TMP_FontAsset;

        // 字体资源健康检查
        if (fontAsset != null)
        {
            CheckFontAssetHealth();
        }

        EditorGUILayout.Space();
        EditorGUILayout.LabelField("字符添加选项", EditorStyles.boldLabel);

        // JSON文件导入功能
        showJsonImport = EditorGUILayout.Foldout(showJsonImport, "从JSON文件导入汉字");
        if (showJsonImport)
        {
            DrawJsonImportSection();
        }

        useCustomCharacterList = EditorGUILayout.Toggle("使用自定义字符列表", useCustomCharacterList);
        
        if (useCustomCharacterList)
        {
            EditorGUILayout.HelpBox("输入需要添加的字符", MessageType.Info);
            charactersToAdd = EditorGUILayout.TextArea(charactersToAdd, GUILayout.Height(100));
        }
        else
        {
            EditorGUILayout.HelpBox("将添加默认字符: 流", MessageType.Info);
        }

        EditorGUILayout.Space();
        showAdvancedOptions = EditorGUILayout.Foldout(showAdvancedOptions, "高级设置");
        if (showAdvancedOptions)
        {
            EditorGUILayout.LabelField("图集设置", EditorStyles.boldLabel);
            atlasWidth = EditorGUILayout.IntPopup("图集宽度", atlasWidth, new string[] { "32", "64", "128", "256", "512", "1024", "2048", "4096", "8192" }, new int[] { 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192 });
            atlasHeight = EditorGUILayout.IntPopup("图集高度", atlasHeight, new string[] { "32", "64", "128", "256", "512", "1024", "2048", "4096", "8192" }, new int[] { 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192 });
            atlasPadding = EditorGUILayout.IntSlider("图集内边距", atlasPadding, 1, 10);
            renderMode = (GlyphRenderMode)EditorGUILayout.EnumPopup("渲染模式", renderMode);
        }

        showPreview = EditorGUILayout.Foldout(showPreview, "字符预览");
        if (showPreview)
        {
            EditorGUILayout.LabelField("将添加的字符:");
            EditorGUILayout.SelectableLabel(charactersToAdd, EditorStyles.textField, GUILayout.Height(50));
            
            if (!string.IsNullOrEmpty(charactersToAdd))
            {
                EditorGUILayout.LabelField("Unicode 值:");
                string unicodeValues = GetUnicodeValues(charactersToAdd);
                EditorGUILayout.SelectableLabel(unicodeValues, EditorStyles.textField, GUILayout.Height(50));
            }
        }

        EditorGUILayout.Space();
        
        GUI.enabled = fontAsset != null && !string.IsNullOrEmpty(charactersToAdd);
        if (GUILayout.Button("添加字符到字体资源"))
        {
            AddCharactersToFontAsset();
        }

        if (fontAsset != null && !string.IsNullOrEmpty(charactersToAdd) && showAdvancedOptions)
        {
            if (GUILayout.Button("重新生成字体资源"))
            {
                RegenerateFontAsset();
            }
        }
        GUI.enabled = true;
    }

    private void QueryUnicode()
    {
        if (string.IsNullOrEmpty(unicodeInput))
        {
            unicodeQueryResult = "请输入有效的Unicode值";
            return;
        }

        if (TryGetCharFromUnicode(unicodeInput, out char character))
        {
            unicodeQueryResult = $"Unicode \\u{unicodeInput} 对应的字符是: {character}\n" +
                               $"字符名称: {GetCharacterName(character)}";
        }
        else
        {
            unicodeQueryResult = "无效的Unicode值，请使用有效的十六进制值";
        }
    }

    private bool TryGetCharFromUnicode(string unicodeHex, out char character)
    {
        character = '\0';
        try
        {
            if (int.TryParse(unicodeHex, NumberStyles.HexNumber, CultureInfo.InvariantCulture, out int unicodeValue))
            {
                if (unicodeValue >= 0 && unicodeValue <= 0xFFFF)
                {
                    character = (char)unicodeValue;
                    return true;
                }
            }
        }
        catch
        {
            // 解析失败
        }
        return false;
    }

    private string GetCharacterName(char c)
    {
        // 返回常见中文字符的名称
        switch (c)
        {
            case '树': return "树 (shù) - 树木、树林的'树'";
            case '松': return "松 (sōng) - 松树的'松'";
            case '流': return "流 (liú) - 流水的'流'";
            default:
                if (c >= 0x4E00 && c <= 0x9FFF)
                    return "汉字";
                else
                    return "未知字符";
        }
    }

    private void AddCharactersToFontAsset()
    {
        if (fontAsset == null || string.IsNullOrEmpty(charactersToAdd))
            return;

        try
        {
            // 去重处理
            HashSet<uint> uniqueCharacters = new HashSet<uint>();
            foreach (char c in charactersToAdd)
            {
                uniqueCharacters.Add(c);
            }
            
            // 创建字符集
            uint[] characterSet = new uint[uniqueCharacters.Count];
            int index = 0;
            foreach (uint c in uniqueCharacters)
            {
                characterSet[index++] = c;
            }
            
            // 使用TMP_FontAsset.TryAddCharacters方法
            bool result = fontAsset.TryAddCharacters(characterSet);
            
            // 标记资源为已修改并保存
            EditorUtility.SetDirty(fontAsset);
            AssetDatabase.SaveAssets();
            
            if (result)
            {
                Debug.Log($"成功添加字符到字体资源 {fontAsset.name}");
                EditorUtility.DisplayDialog("成功", "已成功添加字符到字体资源", "确定");
            }
            else
            {
                Debug.LogWarning($"部分或全部字符无法添加到字体资源 {fontAsset.name}");
                EditorUtility.DisplayDialog("警告", "部分或全部字符无法添加到字体资源。可能需要重新生成字体资源。", "确定");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"添加字符时出错: {e.Message}");
            EditorUtility.DisplayDialog("错误", $"添加字符时出错: {e.Message}", "确定");
        }
    }

    private void RegenerateFontAsset()
    {
        if (fontAsset == null || string.IsNullOrEmpty(charactersToAdd))
            return;

        try
        {
            // 获取源字体
            Font sourceFont = fontAsset.sourceFontFile;
            if (sourceFont == null)
            {
                EditorUtility.DisplayDialog("错误", "无法获取源字体文件，请确保字体资源包含源字体引用", "确定");
                return;
            }

            // 去重处理
            HashSet<uint> uniqueCharacters = new HashSet<uint>();
            foreach (char c in charactersToAdd)
            {
                uniqueCharacters.Add(c);
            }

            // 创建字符集
            string characterSequence = new string(charactersToAdd.ToCharArray().Distinct().ToArray());
            
            Debug.Log("开始重新生成字体资源...");
            
            // 创建新的字体资源
            TMP_FontAsset newFontAsset = TMP_FontAsset.CreateFontAsset(
                sourceFont, 
                fontAsset.faceInfo.pointSize, 
                atlasPadding, 
                renderMode, 
                atlasWidth, 
                atlasHeight);
            
            if (newFontAsset != null)
            {
                // 添加字符
                uint[] characterSet = new uint[characterSequence.Length];
                for (int i = 0; i < characterSequence.Length; i++)
                {
                    characterSet[i] = characterSequence[i];
                }
                
                // 尝试添加字符
                bool addResult = newFontAsset.TryAddCharacters(characterSet);
                
                if (!addResult)
                {
                    Debug.LogWarning("部分字符可能无法添加");
                }
                
                // 保存字体资源
                string assetPath = AssetDatabase.GetAssetPath(fontAsset);
                string directory = Path.GetDirectoryName(assetPath);
                string fileName = Path.GetFileNameWithoutExtension(assetPath) + "_Regenerated.asset";
                string fullOutputPath = Path.Combine(directory, fileName);
                
                AssetDatabase.CreateAsset(newFontAsset, fullOutputPath);
                
                // 保存材质
                Material material = new Material(Shader.Find("TextMeshPro/Distance Field"));
                material.SetTexture("_MainTex", newFontAsset.atlasTexture);
                material.SetFloat("_TextureWidth", newFontAsset.atlasWidth);
                material.SetFloat("_TextureHeight", newFontAsset.atlasHeight);
                
                AssetDatabase.AddObjectToAsset(material, newFontAsset);
                newFontAsset.material = material;
                
                EditorUtility.SetDirty(newFontAsset);
                AssetDatabase.SaveAssets();
                
                Debug.Log($"字体资源已重新生成并保存到: {fullOutputPath}");
                EditorUtility.DisplayDialog("成功", $"字体资源已成功重新生成\n保存路径: {fullOutputPath}", "确定");
            }
            else
            {
                Debug.LogError("无法创建字体资源");
                EditorUtility.DisplayDialog("错误", "无法创建字体资源", "确定");
            }
            
            // 刷新资源数据库
            AssetDatabase.Refresh();
        }
        catch (System.Exception e)
        {
            Debug.LogError($"重新生成字体资源时出错: {e.Message}");
            EditorUtility.DisplayDialog("错误", $"重新生成字体资源时出错: {e.Message}", "确定");
        }
    }

    private string GetUnicodeValues(string text)
    {
        if (string.IsNullOrEmpty(text))
            return "";

        StringBuilder sb = new StringBuilder();
        foreach (char c in text)
        {
            sb.AppendLine($"\\u{(int)c:X4} ({c})");
        }
        return sb.ToString();
    }

    private void DrawJsonImportSection()
    {
        EditorGUILayout.BeginVertical("box");

        EditorGUILayout.LabelField("JSON文件列表", EditorStyles.boldLabel);

        // 拖拽区域
        Rect dropArea = GUILayoutUtility.GetRect(0.0f, 50.0f, GUILayout.ExpandWidth(true));
        GUI.Box(dropArea, "拖拽JSON文件到这里");

        HandleDragAndDrop(dropArea);

        // 显示已添加的文件列表
        if (jsonFilePaths.Count > 0)
        {
            EditorGUILayout.LabelField($"已添加 {jsonFilePaths.Count} 个文件:");
            jsonScrollPosition = EditorGUILayout.BeginScrollView(jsonScrollPosition, GUILayout.Height(100));

            for (int i = jsonFilePaths.Count - 1; i >= 0; i--)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField(Path.GetFileName(jsonFilePaths[i]), GUILayout.ExpandWidth(true));
                if (GUILayout.Button("移除", GUILayout.Width(50)))
                {
                    jsonFilePaths.RemoveAt(i);
                }
                EditorGUILayout.EndHorizontal();
            }

            EditorGUILayout.EndScrollView();

            // 提取选项
            EditorGUILayout.LabelField("提取选项:", EditorStyles.boldLabel);
            extractFromWords = EditorGUILayout.Toggle("从词语中提取汉字", extractFromWords);
            extractFromSentences = EditorGUILayout.Toggle("从句子中提取汉字", extractFromSentences);

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("提取汉字"))
            {
                ExtractCharactersFromJsonFiles();
            }
            if (GUILayout.Button("清空列表"))
            {
                jsonFilePaths.Clear();
                extractedCharacters.Clear();
            }
            EditorGUILayout.EndHorizontal();

            // 显示提取的汉字
            if (extractedCharacters.Count > 0)
            {
                EditorGUILayout.LabelField($"提取到 {extractedCharacters.Count} 个汉字:");
                string extractedText = new string(extractedCharacters.ToArray());
                EditorGUILayout.SelectableLabel(extractedText, EditorStyles.textField, GUILayout.Height(60));

                if (GUILayout.Button("添加到字符列表"))
                {
                    charactersToAdd += extractedText;
                    useCustomCharacterList = true;
                }
            }
        }

        EditorGUILayout.EndVertical();
    }

    private void HandleDragAndDrop(Rect dropArea)
    {
        Event evt = Event.current;

        switch (evt.type)
        {
            case EventType.DragUpdated:
            case EventType.DragPerform:
                if (!dropArea.Contains(evt.mousePosition))
                    return;

                DragAndDrop.visualMode = DragAndDropVisualMode.Copy;

                if (evt.type == EventType.DragPerform)
                {
                    DragAndDrop.AcceptDrag();

                    foreach (string draggedPath in DragAndDrop.paths)
                    {
                        if (Path.GetExtension(draggedPath).ToLower() == ".json")
                        {
                            if (!jsonFilePaths.Contains(draggedPath))
                            {
                                jsonFilePaths.Add(draggedPath);
                                Debug.Log($"添加JSON文件: {draggedPath}");
                            }
                        }
                        else
                        {
                            Debug.LogWarning($"跳过非JSON文件: {draggedPath}");
                        }
                    }
                }
                break;
        }
    }

    private void ExtractCharactersFromJsonFiles()
    {
        extractedCharacters.Clear();

        foreach (string filePath in jsonFilePaths)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    string jsonContent = File.ReadAllText(filePath);
                    ExtractCharactersFromJson(jsonContent);
                    Debug.Log($"从文件 {Path.GetFileName(filePath)} 提取汉字完成");
                }
                else
                {
                    Debug.LogWarning($"文件不存在: {filePath}");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"处理文件 {filePath} 时出错: {e.Message}");
            }
        }

        Debug.Log($"总共提取到 {extractedCharacters.Count} 个不重复的汉字");
    }

    private void ExtractCharactersFromJson(string jsonContent)
    {
        try
        {
            JObject jsonObj = JObject.Parse(jsonContent);
            JObject characters = jsonObj["characters"] as JObject;

            if (characters == null)
            {
                Debug.LogWarning("JSON文件中未找到 'characters' 节点");
                return;
            }

            foreach (var characterPair in characters)
            {
                string charKey = characterPair.Key;
                JObject charData = characterPair.Value as JObject;

                // 添加汉字本身
                if (!string.IsNullOrEmpty(charKey))
                {
                    foreach (char c in charKey)
                    {
                        if (IsChineseCharacter(c))
                        {
                            extractedCharacters.Add(c);
                        }
                    }
                }

                // 从学习内容中提取汉字
                JObject learningContent = charData?["learning_content"] as JObject;
                if (learningContent != null)
                {
                    // 从词语中提取
                    if (extractFromWords)
                    {
                        JObject word = learningContent["word"] as JObject;
                        if (word != null)
                        {
                            string wordText = word["text"]?.ToString();
                            if (!string.IsNullOrEmpty(wordText))
                            {
                                ExtractChineseCharacters(wordText);
                            }
                        }
                    }

                    // 从句子中提取
                    if (extractFromSentences)
                    {
                        JObject sentence = learningContent["sentence"] as JObject;
                        if (sentence != null)
                        {
                            string sentenceText = sentence["text"]?.ToString();
                            if (!string.IsNullOrEmpty(sentenceText))
                            {
                                ExtractChineseCharacters(sentenceText);
                            }
                        }
                    }
                }
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"解析JSON时出错: {e.Message}");
        }
    }

    private void ExtractChineseCharacters(string text)
    {
        if (string.IsNullOrEmpty(text))
            return;

        foreach (char c in text)
        {
            if (IsChineseCharacter(c))
            {
                extractedCharacters.Add(c);
            }
        }
    }

    private bool IsChineseCharacter(char c)
    {
        // 判断是否为中文字符（基本汉字范围）
        return c >= 0x4E00 && c <= 0x9FFF;
    }

    /// <summary>
    /// 检查字体资源健康状态
    /// </summary>
    private void CheckFontAssetHealth()
    {
        if (fontAsset == null) return;

        bool hasIssues = false;
        string issueMessage = "";

        // 检查图集纹理
        if (fontAsset.atlasTexture == null)
        {
            hasIssues = true;
            issueMessage += "• 图集纹理丢失\n";
        }

        // 检查材质
        if (fontAsset.material == null)
        {
            hasIssues = true;
            issueMessage += "• 材质丢失\n";
        }

        // 检查源字体文件
        if (fontAsset.sourceFontFile == null)
        {
            hasIssues = true;
            issueMessage += "• 源字体文件丢失\n";
        }

        // 检查字符表
        if (fontAsset.characterTable == null || fontAsset.characterTable.Count == 0)
        {
            hasIssues = true;
            issueMessage += "• 字符表为空\n";
        }

        // 显示检查结果
        if (hasIssues)
        {
            EditorGUILayout.HelpBox($"字体资源存在问题：\n{issueMessage}建议重新生成字体资源", MessageType.Error);

            if (GUILayout.Button("尝试自动修复"))
            {
                AttemptAutoFix();
            }
        }
        else
        {
            EditorGUILayout.HelpBox("字体资源状态正常", MessageType.Info);
        }
    }

    /// <summary>
    /// 尝试自动修复字体资源
    /// </summary>
    private void AttemptAutoFix()
    {
        if (fontAsset == null) return;

        try
        {
            // 如果有源字体文件，尝试重新生成
            if (fontAsset.sourceFontFile != null)
            {
                if (EditorUtility.DisplayDialog("自动修复",
                    "将重新生成字体资源，这可能需要一些时间。是否继续？", "继续", "取消"))
                {
                    // 使用当前字符列表重新生成
                    if (!string.IsNullOrEmpty(charactersToAdd))
                    {
                        RegenerateFontAsset();
                    }
                    else
                    {
                        EditorUtility.DisplayDialog("提示",
                            "请先在字符列表中添加需要的字符，然后使用'重新生成字体资源'功能", "确定");
                    }
                }
            }
            else
            {
                EditorUtility.DisplayDialog("无法修复",
                    "源字体文件丢失，无法自动修复。请手动重新创建字体资源。", "确定");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"自动修复失败: {e.Message}");
            EditorUtility.DisplayDialog("修复失败", $"自动修复失败: {e.Message}", "确定");
        }
    }
}